#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
预测模型模块
实现流量预测、运行状态推演、停止起降情景推演等核心算法
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import random
from typing import Dict, List, Tuple, Optional

class FlightTrafficPredictor:
    """航班流量预测器"""
    
    def __init__(self):
        self.historical_data = None
        self.weather_data = None
    
    def load_data(self, flight_file: str, weather_file: str = None):
        """
        加载航班数据

        Args:
            flight_file: 航班数据文件路径
            weather_file: 天气数据文件路径（暂不使用）
        """
        self.historical_data = pd.read_excel(flight_file)
        print(f"已加载航班数据: {len(self.historical_data)} 条记录")
    
    def predict_hourly_traffic(self, target_date: str, weather_forecast: Dict = None) -> pd.DataFrame:
        """
        基于航班计划数据预测指定日期的小时流量

        Args:
            target_date: 目标日期 (YYYY-MM-DD)
            weather_forecast: 天气预报数据（暂不使用）

        Returns:
            DataFrame: 包含预测和实际流量对比的数据表
        """
        if self.historical_data is None:
            raise ValueError("请先加载航班数据")

        # 筛选目标日期的数据
        df = self.historical_data[self.historical_data['date'] == target_date].copy()

        if df.empty:
            raise ValueError(f"未找到日期 {target_date} 的航班数据")

        # 计算计划流量（基于计划时间）
        planned_traffic = self._calculate_planned_traffic(df)

        # 计算实际流量（基于实际时间）
        actual_traffic = self._calculate_actual_traffic(df)

        # 合并数据并计算偏差
        result_df = self._merge_and_calculate_deviation(planned_traffic, actual_traffic)

        return result_df

    def _calculate_planned_traffic(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算基于计划时间的每小时流量"""
        traffic_data = []

        for hour in range(7, 24):  # 7:00-23:59
            hour_str = f"{hour:02d}"

            # 计划进港流量（到达ZGGG的航班）
            planned_arrivals = df[
                (df['planned_arr_airport'] == 'ZGGG') &
                (df['planned_arr_time'].str.startswith(hour_str))
            ]

            # 计划出港流量（从ZGGG出发的航班）
            planned_departures = df[
                (df['planned_dep_airport'] == 'ZGGG') &
                (df['planned_dep_time'].str.startswith(hour_str))
            ]

            traffic_data.append({
                'hour': hour,
                'planned_inbound': len(planned_arrivals),
                'planned_outbound': len(planned_departures),
                'planned_total': len(planned_arrivals) + len(planned_departures)
            })

        return pd.DataFrame(traffic_data)

    def _calculate_actual_traffic(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算基于实际时间的每小时流量"""
        traffic_data = []

        for hour in range(7, 24):  # 7:00-23:59
            hour_str = f"{hour:02d}"

            # 实际进港流量（到达ZGGG的航班）
            actual_arrivals = df[
                (df['actual_arr_airport'] == 'ZGGG') &
                (df['actual_arr_time'].str.startswith(hour_str))
            ]

            # 实际出港流量（从ZGGG出发的航班）
            actual_departures = df[
                (df['actual_dep_airport'] == 'ZGGG') &
                (df['actual_dep_time'].str.startswith(hour_str))
            ]

            traffic_data.append({
                'hour': hour,
                'actual_inbound': len(actual_arrivals),
                'actual_outbound': len(actual_departures),
                'actual_total': len(actual_arrivals) + len(actual_departures)
            })

        return pd.DataFrame(traffic_data)

    def _merge_and_calculate_deviation(self, planned_df: pd.DataFrame, actual_df: pd.DataFrame) -> pd.DataFrame:
        """合并计划和实际数据，计算偏差率"""
        # 合并数据
        result_df = planned_df.merge(actual_df, on='hour', how='outer')

        # 计算偏差率
        result_df['deviation_rate'] = result_df.apply(
            lambda row: abs(row['planned_total'] - row['actual_total']) / row['actual_total']
            if row['actual_total'] > 0 else 0, axis=1
        )

        # 添加备注列
        result_df['remark'] = result_df['deviation_rate'].apply(
            lambda x: '>15%' if x > 0.15 else '正常'
        )

        # 格式化时段列
        result_df['time_period'] = result_df['hour'].apply(lambda x: f"{x:02d}:00-{x:02d}:59")

        # 重新排列列顺序
        result_df = result_df[[
            'time_period', 'planned_inbound', 'planned_outbound', 'planned_total',
            'actual_inbound', 'actual_outbound', 'actual_total', 'deviation_rate', 'remark'
        ]]

        # 重命名列
        result_df.columns = [
            '时段', '预测进港量', '预测出港量', '预测总流量',
            '实际进港量', '实际出港量', '实际总流量', '小时偏差率', '备注'
        ]

        return result_df
    
    def get_traffic_summary(self, traffic_df: pd.DataFrame) -> Dict:
        """
        获取流量预测汇总统计

        Args:
            traffic_df: 流量预测结果DataFrame

        Returns:
            Dict: 汇总统计信息
        """
        summary = {
            'total_planned_flights': traffic_df['预测总流量'].sum(),
            'total_actual_flights': traffic_df['实际总流量'].sum(),
            'average_deviation_rate': traffic_df['小时偏差率'].mean(),
            'max_deviation_rate': traffic_df['小时偏差率'].max(),
            'over_15_percent_count': len(traffic_df[traffic_df['小时偏差率'] > 0.15]),
            'peak_hour_planned': traffic_df.loc[traffic_df['预测总流量'].idxmax(), '时段'],
            'peak_hour_actual': traffic_df.loc[traffic_df['实际总流量'].idxmax(), '时段'],
            'meets_criteria': (
                traffic_df['小时偏差率'].mean() <= 0.1 and
                len(traffic_df[traffic_df['小时偏差率'] > 0.15]) <= 2
            )
        }

        return summary

class FlightOperationSimulator:
    """航班运行状态推演器"""

    def __init__(self):
        self.capacity_per_hour = 20  # 每小时容量
        self.delay_threshold = 10    # 积压阈值（航班数）

    def simulate_24h_operations(self, hourly_traffic: Dict, disruptions: List = None) -> Dict:
        """
        模拟24小时航班运行状态

        Args:
            hourly_traffic: 小时流量预测
            disruptions: 干扰事件列表 [{'start_hour': int, 'end_hour': int, 'type': str}]

        Returns:
            Dict: 运行状态推演结果
        """
        results = {
            'hourly_status': {},
            'backlog_periods': [],
            'peak_backlog': {'hour': 0, 'count': 0},
            'last_operation_hour': 23
        }

        current_backlog = 0
        backlog_start = None

        for hour in range(24):
            # 获取当前小时的计划流量
            planned_flights = hourly_traffic.get(hour, {'total': 0})['total']

            # 检查是否有干扰
            capacity = self.capacity_per_hour
            if disruptions:
                for disruption in disruptions:
                    if disruption['start_hour'] <= hour <= disruption['end_hour']:
                        if disruption['type'] == 'stop_operations':
                            capacity = 0
                        elif disruption['type'] == 'reduced_capacity':
                            capacity = int(capacity * 0.5)

            # 计算实际处理能力
            total_demand = current_backlog + planned_flights
            processed = min(total_demand, capacity)
            current_backlog = max(0, total_demand - processed)

            # 记录状态
            results['hourly_status'][hour] = {
                'planned_flights': planned_flights,
                'backlog_start': current_backlog,
                'capacity': capacity,
                'processed': processed,
                'backlog_end': current_backlog,
                'is_congested': current_backlog > self.delay_threshold
            }

            # 跟踪积压时段
            if current_backlog > self.delay_threshold:
                if backlog_start is None:
                    backlog_start = hour
            else:
                if backlog_start is not None:
                    results['backlog_periods'].append({
                        'start': backlog_start,
                        'end': hour - 1,
                        'duration': hour - backlog_start
                    })
                    backlog_start = None

            # 更新峰值积压
            if current_backlog > results['peak_backlog']['count']:
                results['peak_backlog'] = {'hour': hour, 'count': current_backlog}

        # 处理最后的积压时段
        if backlog_start is not None:
            results['backlog_periods'].append({
                'start': backlog_start,
                'end': 23,
                'duration': 24 - backlog_start
            })

        # 计算最晚运行时段
        if current_backlog > 0:
            # 估算清理积压需要的额外时间
            extra_hours = int(np.ceil(current_backlog / self.capacity_per_hour))
            results['last_operation_hour'] = min(23 + extra_hours, 30)  # 最多到次日6点

        return results

    def calculate_backlog_deviation(self, predicted_result: Dict, actual_result: Dict) -> Dict:
        """
        计算积压时段偏移误差

        Args:
            predicted_result: 预测推演结果
            actual_result: 实际运行结果

        Returns:
            Dict: 偏移误差统计
        """
        deviation_analysis = {
            'period_deviation': 0,
            'duration_match': False,
            'peak_deviation': 0,
            'last_operation_match': False,
            'meets_criteria': False
        }

        # 分析积压时段偏差
        if predicted_result['backlog_periods'] and actual_result['backlog_periods']:
            pred_period = predicted_result['backlog_periods'][0]
            actual_period = actual_result['backlog_periods'][0]

            # 时段偏差（开始和结束时间）
            start_deviation = abs(pred_period['start'] - actual_period['start'])
            end_deviation = abs(pred_period['end'] - actual_period['end'])
            deviation_analysis['period_deviation'] = max(start_deviation, end_deviation)

            # 持续时长匹配
            deviation_analysis['duration_match'] = pred_period['duration'] == actual_period['duration']

            # 积压峰值偏差
            pred_peak = predicted_result['peak_backlog']['count']
            actual_peak = actual_result['peak_backlog']['count']
            if actual_peak > 0:
                peak_deviation_rate = abs(pred_peak - actual_peak) / actual_peak
                deviation_analysis['peak_deviation'] = peak_deviation_rate

            # 最晚运行时段匹配
            deviation_analysis['last_operation_match'] = (
                predicted_result['last_operation_hour'] == actual_result['last_operation_hour']
            )

            # 综合评估是否满足标准
            deviation_analysis['meets_criteria'] = (
                deviation_analysis['period_deviation'] <= 1 and
                deviation_analysis['duration_match'] and
                deviation_analysis['peak_deviation'] <= 0.15 and
                deviation_analysis['last_operation_match']
            )

        return deviation_analysis

class OptimalDecisionMaker:
    """最优决策方案生成器"""

    def __init__(self):
        self.delay_cost_per_minute = 1.0  # 每分钟延误成本

    def generate_optimal_delay_plan(self, hourly_traffic: Dict, capacity_constraints: Dict) -> Dict:
        """
        生成最优延误决策方案

        Args:
            hourly_traffic: 小时流量数据
            capacity_constraints: 容量约束

        Returns:
            Dict: 最优延误方案
        """
        delay_plan = {
            'total_delay_minutes': 0,
            'affected_flights': [],
            'hourly_adjustments': {}
        }

        current_backlog = 0

        for hour in range(24):
            planned_flights = hourly_traffic.get(hour, {'total': 0})['total']
            capacity = capacity_constraints.get(hour, 20)

            total_demand = current_backlog + planned_flights

            if total_demand > capacity:
                # 需要延误部分航班
                flights_to_delay = total_demand - capacity

                # 简单策略：优先延误后续时段容量较大的航班
                delay_minutes = random.randint(30, 120)

                delay_plan['affected_flights'].extend([
                    {
                        'original_hour': hour,
                        'delay_minutes': delay_minutes,
                        'new_hour': hour + (delay_minutes // 60),
                        'flight_id': f"Flight_{hour}_{i}"
                    }
                    for i in range(flights_to_delay)
                ])

                delay_plan['total_delay_minutes'] += flights_to_delay * delay_minutes
                current_backlog = 0
            else:
                current_backlog = max(0, total_demand - capacity)

            delay_plan['hourly_adjustments'][hour] = {
                'original_demand': total_demand,
                'capacity': capacity,
                'delayed_flights': flights_to_delay if total_demand > capacity else 0
            }

        return delay_plan


class FlightDelayPredictor:
    """航班延误预测器"""

    def __init__(self):
        self.delay_threshold = 15  # 固定延误阈值15分钟
        self.historical_data = None

    def load_data(self, flight_file: str):
        """
        加载航班数据

        Args:
            flight_file: 航班数据文件路径
        """
        self.historical_data = pd.read_excel(flight_file)
        print(f"延误预测器已加载航班数据: {len(self.historical_data)} 条记录")

    def predict_flight_delays(self, target_date: str) -> pd.DataFrame:
        """
        预测指定日期每个航班的延误情况

        Args:
            target_date: 目标日期 (YYYY-MM-DD)

        Returns:
            DataFrame: 包含每个航班延误预测的数据表
        """
        if self.historical_data is None:
            raise ValueError("请先加载航班数据")

        # 筛选目标日期的出港航班数据
        df = self.historical_data[
            (self.historical_data['date'] == target_date) &
            (self.historical_data['planned_dep_airport'] == 'ZGGG')  # 只预测出港航班
        ].copy()

        if df.empty:
            raise ValueError(f"未找到日期 {target_date} 的出港航班数据")

        # 为每个航班预测延误
        predictions = []

        for _, flight in df.iterrows():
            # 简单随机预测延误值：-50到100分钟
            predicted_delay = self._predict_single_flight_delay(flight)

            # 计算预测离港时间
            predicted_dep_time = self._calculate_predicted_time(
                flight['planned_dep_time'], predicted_delay
            )

            predictions.append({
                '航班号': flight['flight_no'],
                '日期': flight['date'],
                '计划离港时间': flight['planned_dep_time'],
                '实际离港时间': flight['actual_dep_time'],
                '预测离港延误(分钟)': predicted_delay,
                '预测离港时间': predicted_dep_time
            })

        return pd.DataFrame(predictions)

    def _predict_single_flight_delay(self, flight_data: pd.Series) -> int:
        """
        预测单个航班的延误时间

        Args:
            flight_data: 航班数据行

        Returns:
            int: 预测延误时间（分钟，-50到100之间）
        """
        # 设置随机种子，基于航班号确保预测结果一致
        flight_seed = hash(flight_data['flight_no']) % 10000
        np.random.seed(flight_seed)

        # 简单随机预测：-50到100分钟
        delay_minutes = np.random.randint(-50, 101)

        return delay_minutes

    def _calculate_predicted_time(self, planned_time: str, delay_minutes: int) -> str:
        """
        计算预测离港时间

        Args:
            planned_time: 计划离港时间 (HH:MM格式)
            delay_minutes: 延误分钟数

        Returns:
            str: 预测离港时间 (HH:MM格式)
        """
        from datetime import datetime, timedelta

        try:
            # 解析计划时间
            planned_dt = datetime.strptime(planned_time, '%H:%M')

            # 加上延误时间
            predicted_dt = planned_dt + timedelta(minutes=delay_minutes)

            # 返回格式化的时间
            return predicted_dt.strftime('%H:%M')

        except Exception:
            # 如果计算失败，返回原计划时间
            return planned_time
