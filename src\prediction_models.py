#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
预测模型模块
实现流量预测、运行状态推演、停止起降情景推演等核心算法
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import random
import os
import warnings
from typing import Dict, List, Tuple, Optional

# 深度学习相关导入
try:
    import torch
    import torch.nn as nn
    from torch.utils.data import Dataset, DataLoader
    from torchvision import models, transforms
    from transformers import BertTokenizer, BertModel
    from sklearn.preprocessing import StandardScaler
    from PIL import Image
    import math
    from tqdm import tqdm
    DEEP_LEARNING_AVAILABLE = True
except ImportError:
    DEEP_LEARNING_AVAILABLE = False
    print("深度学习依赖未安装，将使用简化版流量预测算法")

warnings.filterwarnings('ignore')


# 深度学习模型配置和类
if DEEP_LEARNING_AVAILABLE:
    class Config:
        """深度学习模型配置"""
        def __init__(self, base_path="."):
            self.BASE_PATH = base_path
            self.BERT_MODEL_PATH = os.path.join(base_path, 'model', 'bert_models')
            self.RESNET_WEIGHTS_PATH = os.path.join(base_path, 'model', 'resnet50-0676ba61.pth')
            self.MODEL_WEIGHTS_PATH = os.path.join(base_path, 'model', 'best_transformer_model.pth')

            self.HISTORICAL_DAYS = 7
            self.INPUT_SEQ_LEN = self.HISTORICAL_DAYS + 1
            self.IMG_SIZE = 224
            self.EMBED_DIM = 512
            self.NUM_TABULAR_FEATURES = 2 + 16

            # Transformer配置
            self.TRANSFORMER_NHEAD = 8
            self.TRANSFORMER_ENCODER_LAYERS = 5
            self.TRANSFORMER_DIM_FEEDFORWARD = 2048
            self.DROPOUT = 0.1
            self.BATCH_SIZE = 6

            self.DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    class PositionalEncoding(nn.Module):
        """位置编码"""
        def __init__(self, d_model, dropout=0.1, max_len=5000):
            super(PositionalEncoding, self).__init__()
            self.dropout = nn.Dropout(p=dropout)
            position = torch.arange(max_len).unsqueeze(1)
            div_term = torch.exp(torch.arange(0, d_model, 2) * (-math.log(10000.0) / d_model))
            pe = torch.zeros(max_len, 1, d_model)
            pe[:, 0, 0::2] = torch.sin(position * div_term)
            pe[:, 0, 1::2] = torch.cos(position * div_term)
            self.register_buffer('pe', pe)

        def forward(self, x):
            x = x + self.pe[:x.size(0)]
            return self.dropout(x)

    class FlightTrafficModel(nn.Module):
        """深度学习流量预测模型"""
        def __init__(self, cfg, bert_model):
            super(FlightTrafficModel, self).__init__()
            self.cfg = cfg

            # 图像特征提取器
            resnet = models.resnet50(weights=None)
            if os.path.exists(cfg.RESNET_WEIGHTS_PATH):
                resnet.load_state_dict(torch.load(cfg.RESNET_WEIGHTS_PATH, map_location=cfg.DEVICE))
            self.image_feature_extractor = nn.Sequential(*list(resnet.children())[:-1])
            self.image_aggregator = nn.GRU(input_size=2048, hidden_size=cfg.EMBED_DIM, batch_first=True)

            # 文本特征提取器
            self.text_feature_extractor = bert_model
            self.text_aggregator = nn.GRU(input_size=bert_model.config.hidden_size, hidden_size=cfg.EMBED_DIM, batch_first=True)

            # 表格数据投影器
            self.tabular_projector = nn.Linear(cfg.NUM_TABULAR_FEATURES, cfg.EMBED_DIM)

            # 融合层
            self.fusion_projector = nn.Linear(cfg.EMBED_DIM * 3, cfg.EMBED_DIM)

            # Transformer编码器
            self.pos_encoder = PositionalEncoding(cfg.EMBED_DIM, cfg.DROPOUT)
            encoder_layer = nn.TransformerEncoderLayer(
                d_model=cfg.EMBED_DIM, nhead=cfg.TRANSFORMER_NHEAD,
                dim_feedforward=cfg.TRANSFORMER_DIM_FEEDFORWARD,
                dropout=cfg.DROPOUT, batch_first=True
            )
            self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers=cfg.TRANSFORMER_ENCODER_LAYERS)

            # 预测头
            self.prediction_head = nn.Sequential(
                nn.LayerNorm(cfg.EMBED_DIM),
                nn.Linear(cfg.EMBED_DIM, cfg.EMBED_DIM // 2),
                nn.ReLU(),
                nn.Dropout(cfg.DROPOUT),
                nn.Linear(cfg.EMBED_DIM // 2, 2)
            )

        def forward(self, batch):
            B, S, *_ = batch['tabular_input'].shape

            fused_features_seq = []
            for t in range(S):
                # 图像特征
                images_t = batch['image_input'][:, t, :, :, :, :]
                N_imgs = images_t.shape[1]
                img_flat = images_t.reshape(B * N_imgs, *images_t.shape[2:])
                img_feats = self.image_feature_extractor(img_flat).view(B, N_imgs, -1)
                _, img_embedding = self.image_aggregator(img_feats)
                img_embedding = img_embedding.squeeze(0)

                # 文本特征
                text_ids_t = batch['text_input_ids'][:, t, :, :]
                text_mask_t = batch['text_attention_mask'][:, t, :, :]
                N_texts = text_ids_t.shape[1]
                text_ids_flat = text_ids_t.reshape(B * N_texts, -1)
                text_mask_flat = text_mask_t.reshape(B * N_texts, -1)
                text_outputs = self.text_feature_extractor(input_ids=text_ids_flat, attention_mask=text_mask_flat)
                text_cls_feats = text_outputs.last_hidden_state[:, 0, :].view(B, N_texts, -1)
                _, text_embedding = self.text_aggregator(text_cls_feats)
                text_embedding = text_embedding.squeeze(0)

                # 表格特征
                tabular_input_t = batch['tabular_input'][:, t, :]
                tabular_embedding = self.tabular_projector(tabular_input_t)

                # 融合特征
                fused_t = torch.cat([img_embedding, text_embedding, tabular_embedding], dim=1)
                fused_features_seq.append(self.fusion_projector(fused_t))

            # Transformer编码
            encoder_input = torch.stack(fused_features_seq, dim=1)
            encoder_input = self.pos_encoder(encoder_input.permute(1, 0, 2)).permute(1, 0, 2)
            encoder_output = self.transformer_encoder(encoder_input)

            # 预测
            final_vector = encoder_output[:, -1, :]
            prediction = self.prediction_head(final_vector)

            return prediction

    class FlightDataset(Dataset):
        """深度学习数据集类"""
        def __init__(self, cfg, tokenizer, df, image_base_path=""):
            self.cfg = cfg
            self.df = df.copy()
            self.tokenizer = tokenizer
            self.image_base_path = image_base_path

            # 确保时间列为datetime类型
            if 'time' in self.df.columns:
                self.df['time'] = pd.to_datetime(self.df['time'])
                self.df.set_index('time', inplace=True)

            # 定义特征列
            self.tabular_cols = ['计划离港数', '计划到港数'] + [f'方向_{d}' for d in
                                                                ['N', 'NNE', 'NE', 'ENE', 'E', 'ESE', 'SE', 'SSE', 'S',
                                                                 'SSW', 'SW', 'WSW', 'W', 'WNW', 'NW', 'NNW']]
            self.target_cols = ['实际离港数', '实际到港数']

            # 标准化数值特征
            self.tabular_scaler = StandardScaler()
            self.df[self.tabular_cols] = self.tabular_scaler.fit_transform(self.df[self.tabular_cols])

            self.target_scaler = StandardScaler()
            self.df[self.target_cols] = self.target_scaler.fit_transform(self.df[self.target_cols])

            # 图像变换
            self.image_transform = transforms.Compose([
                transforms.Resize((cfg.IMG_SIZE, cfg.IMG_SIZE)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
            ])

            self.index_to_time = self.df.index

        def __len__(self):
            return len(self.df) - self.cfg.HISTORICAL_DAYS * 24

        def __getitem__(self, idx):
            target_time_idx = idx + self.cfg.HISTORICAL_DAYS * 24
            target_time = self.index_to_time[target_time_idx]

            # 获取输入特征序列
            input_timestamps = [target_time - pd.Timedelta(days=d) for d in range(self.cfg.HISTORICAL_DAYS, -1, -1)]

            all_tabular_data, all_images, all_texts = [], [], []

            for ts in input_timestamps:
                if ts in self.df.index:
                    row = self.df.loc[ts]
                else:
                    row = pd.Series(0, index=self.df.columns)

                # 数值特征
                all_tabular_data.append(torch.tensor(row[self.tabular_cols].values.astype(np.float32), dtype=torch.float32))

                # 图像特征
                images_for_ts = []
                for i in range(1, 13):
                    img_col = f'雷达图{i}'
                    if img_col in row.index and pd.notna(row[img_col]):
                        img_path = os.path.join(self.image_base_path, str(row[img_col]))
                        if os.path.exists(img_path):
                            try:
                                img = Image.open(img_path).convert('RGB')
                                images_for_ts.append(self.image_transform(img))
                            except:
                                images_for_ts.append(torch.zeros((3, self.cfg.IMG_SIZE, self.cfg.IMG_SIZE)))
                        else:
                            images_for_ts.append(torch.zeros((3, self.cfg.IMG_SIZE, self.cfg.IMG_SIZE)))
                    else:
                        images_for_ts.append(torch.zeros((3, self.cfg.IMG_SIZE, self.cfg.IMG_SIZE)))
                all_images.append(torch.stack(images_for_ts))

                # 文本特征
                texts_for_ts = []
                for i in range(1, 5):
                    texts_for_ts.append(str(row.get(f'天气报文{i}', '')))
                for i in range(1, 5):
                    texts_for_ts.append(str(row.get(f'天气预报{i}', '')))
                for i in range(1, 13):
                    texts_for_ts.append(str(row.get(f'雷达报告{i}', '')))
                all_texts.extend(texts_for_ts)

            # 堆叠特征
            tabular_data_seq = torch.stack(all_tabular_data)
            image_data_seq = torch.stack(all_images)

            # 文本编码
            text_tokens = self.tokenizer(all_texts, padding='max_length', truncation=True, max_length=64, return_tensors='pt')
            num_texts_per_step = 20
            text_ids_seq = text_tokens['input_ids'].view(self.cfg.INPUT_SEQ_LEN, num_texts_per_step, -1)
            text_mask_seq = text_tokens['attention_mask'].view(self.cfg.INPUT_SEQ_LEN, num_texts_per_step, -1)

            # 目标
            target_row = self.df.loc[target_time]
            target = torch.tensor(target_row[self.target_cols].values.astype(np.float32), dtype=torch.float32)

            return {
                'tabular_input': tabular_data_seq,
                'image_input': image_data_seq,
                'text_input_ids': text_ids_seq,
                'text_attention_mask': text_mask_seq,
                'target': target,
                'time_info': str(target_time)
            }


class FlightTrafficPredictor:
    """航班流量预测器 - 支持深度学习和传统算法"""

    def __init__(self, use_deep_learning=True):
        self.historical_data = None
        self.weather_data = None
        self.use_deep_learning = use_deep_learning and DEEP_LEARNING_AVAILABLE

        # 深度学习相关属性
        if self.use_deep_learning:
            self.config = None
            self.model = None
            self.tokenizer = None
            self.bert_model = None
            self.dataset = None
            self.tabular_scaler = None
            self.target_scaler = None
            self._is_model_loaded = False

    def load_data(self, flight_file: str, weather_file: str = None):
        """
        加载航班数据

        Args:
            flight_file: 航班数据文件路径
            weather_file: 天气数据文件路径
        """
        if flight_file.endswith('.csv'):
            self.historical_data = pd.read_csv(flight_file)
        else:
            self.historical_data = pd.read_excel(flight_file)
        print(f"已加载航班数据: {len(self.historical_data)} 条记录")

        # 如果使用深度学习且数据格式符合要求，初始化深度学习模型
        if self.use_deep_learning and self._is_deep_learning_data_format():
            self._initialize_deep_learning_model()

    def _is_deep_learning_data_format(self) -> bool:
        """检查数据是否符合深度学习模型的格式要求"""
        if self.historical_data is None:
            return False

        required_columns = ['time', '计划离港数', '计划到港数', '实际离港数', '实际到港数']
        radar_columns = [f'雷达图{i}' for i in range(1, 13)]

        # 检查基本列是否存在
        has_basic_cols = all(col in self.historical_data.columns for col in required_columns)
        has_radar_cols = any(col in self.historical_data.columns for col in radar_columns)

        return has_basic_cols and has_radar_cols

    def _initialize_deep_learning_model(self):
        """初始化深度学习模型"""
        try:
            # 初始化配置
            self.config = Config()

            # 加载BERT模型和分词器
            if os.path.exists(self.config.BERT_MODEL_PATH):
                self.tokenizer = BertTokenizer.from_pretrained(self.config.BERT_MODEL_PATH)
                self.bert_model = BertModel.from_pretrained(self.config.BERT_MODEL_PATH)

                # 冻结BERT参数
                for param in self.bert_model.parameters():
                    param.requires_grad = False
                self.bert_model.to(self.config.DEVICE)

                # 初始化模型
                self.model = FlightTrafficModel(self.config, self.bert_model)

                # 加载训练好的权重
                if os.path.exists(self.config.MODEL_WEIGHTS_PATH):
                    self.model.load_state_dict(torch.load(self.config.MODEL_WEIGHTS_PATH, map_location=self.config.DEVICE))
                    self.model.to(self.config.DEVICE)
                    self.model.eval()
                    self._is_model_loaded = True
                    print("深度学习模型加载成功")
                else:
                    print(f"警告: 未找到模型权重文件 {self.config.MODEL_WEIGHTS_PATH}")
                    self.use_deep_learning = False
            else:
                print(f"警告: 未找到BERT模型 {self.config.BERT_MODEL_PATH}")
                self.use_deep_learning = False

        except Exception as e:
            print(f"深度学习模型初始化失败: {e}")
            self.use_deep_learning = False

    def predict_hourly_traffic(self, target_date: str, weather_data_path: str = None) -> pd.DataFrame:
        """
        基于航班计划数据预测指定日期的小时流量

        Args:
            target_date: 目标日期 (YYYY-MM-DD)
            weather_data_path: 天气数据路径（用于深度学习模型）

        Returns:
            DataFrame: 包含预测和实际流量对比的数据表
        """
        if self.historical_data is None:
            raise ValueError("请先加载航班数据")

        # 如果使用深度学习模型且模型已加载
        if self.use_deep_learning and self._is_model_loaded:
            return self._predict_with_deep_learning(target_date, weather_data_path)
        else:
            # 使用传统算法
            return self._predict_with_traditional_method(target_date)

    def _predict_with_traditional_method(self, target_date: str) -> pd.DataFrame:
        """使用传统方法进行预测"""
        # 处理时间列
        if 'time' in self.historical_data.columns:
            # 如果有time列，转换为日期进行筛选
            self.historical_data['time'] = pd.to_datetime(self.historical_data['time'])
            target_datetime = pd.to_datetime(target_date)

            # 筛选目标日期的数据
            df = self.historical_data[
                self.historical_data['time'].dt.date == target_datetime.date()
            ].copy()
        elif 'date' in self.historical_data.columns:
            # 如果有date列，直接筛选
            df = self.historical_data[self.historical_data['date'] == target_date].copy()
        else:
            raise ValueError("数据中没有找到时间列（time或date）")

        if df.empty:
            raise ValueError(f"未找到日期 {target_date} 的航班数据")

        # 计算计划流量（基于计划时间）
        planned_traffic = self._calculate_planned_traffic(df)

        # 计算实际流量（基于实际时间）
        actual_traffic = self._calculate_actual_traffic(df)

        # 合并数据并计算偏差
        result_df = self._merge_and_calculate_deviation(planned_traffic, actual_traffic)

        return result_df

    def _predict_with_deep_learning(self, target_date: str, weather_data_path: str = None) -> pd.DataFrame:
        """使用深度学习模型进行预测"""
        try:
            if not self._is_model_loaded:
                print("深度学习模型未加载，使用传统方法")
                return self._predict_with_traditional_method(target_date)

            # 解析目标日期
            target_datetime = pd.to_datetime(target_date)

            # 筛选目标日期的数据
            start_time = target_datetime.replace(hour=0, minute=0, second=0)
            end_time = target_datetime.replace(hour=23, minute=59, second=59)

            # 获取目标日期范围内的数据
            if 'time' in self.historical_data.columns:
                # 如果time是列而不是索引
                time_col = pd.to_datetime(self.historical_data['time'])
                mask = (time_col >= start_time) & (time_col <= end_time)
                target_data = self.historical_data[mask].copy()
            else:
                # 如果time是索引
                mask = (self.historical_data.index >= start_time) & (self.historical_data.index <= end_time)
                target_data = self.historical_data[mask].copy()

            if target_data.empty:
                print(f"未找到日期 {target_date} 的数据，使用传统方法")
                return self._predict_with_traditional_method(target_date)

            # 创建数据集
            image_base_path = weather_data_path if weather_data_path else ""
            dataset = FlightDataset(self.config, self.tokenizer, self.historical_data, image_base_path)

            # 找到目标日期对应的数据集索引
            predictions = []

            for _, row in target_data.iterrows():
                current_time = row.name

                # 检查是否有足够的历史数据
                earliest_time = current_time - pd.Timedelta(days=self.config.HISTORICAL_DAYS)
                if earliest_time not in self.historical_data.index:
                    # 如果没有足够历史数据，使用简单预测
                    pred_depart = row.get('计划离港数', 0)
                    pred_arrive = row.get('计划到港数', 0)
                else:
                    # 使用深度学习模型预测
                    try:
                        # 找到对应的数据集索引
                        time_diff = (current_time - dataset.index_to_time[self.config.HISTORICAL_DAYS * 24]).total_seconds() / 3600
                        dataset_idx = int(time_diff)

                        if 0 <= dataset_idx < len(dataset):
                            sample = dataset[dataset_idx]

                            # 准备批次数据
                            batch = {}
                            for key, value in sample.items():
                                if key != 'time_info':
                                    batch[key] = value.unsqueeze(0).to(self.config.DEVICE)

                            # 模型预测
                            with torch.no_grad():
                                output = self.model(batch)

                            # 逆标准化
                            pred_scaled = output.cpu().numpy()
                            pred_unscaled = dataset.target_scaler.inverse_transform(pred_scaled)

                            pred_depart = max(0, pred_unscaled[0][0])
                            pred_arrive = max(0, pred_unscaled[0][1])
                        else:
                            pred_depart = row.get('计划离港数', 0)
                            pred_arrive = row.get('计划到港数', 0)
                    except Exception as e:
                        print(f"深度学习预测失败，使用计划数据: {e}")
                        pred_depart = row.get('计划离港数', 0)
                        pred_arrive = row.get('计划到港数', 0)

                # 添加预测结果
                predictions.append({
                    '时段': current_time.strftime('%H:%M') + '-' + (current_time + pd.Timedelta(hours=1)).strftime('%H:%M'),
                    '预测进港量': int(pred_arrive),
                    '预测出港量': int(pred_depart),
                    '预测总流量': int(pred_arrive + pred_depart),
                    '实际进港量': int(row.get('实际到港数', 0)),
                    '实际出港量': int(row.get('实际离港数', 0)),
                    '实际总流量': int(row.get('实际到港数', 0) + row.get('实际离港数', 0)),
                    '小时偏差率': 0.0,  # 稍后计算
                    '备注': '正常'
                })

            # 转换为DataFrame并计算偏差率
            result_df = pd.DataFrame(predictions)

            # 计算偏差率
            result_df['小时偏差率'] = result_df.apply(
                lambda row: abs(row['预测总流量'] - row['实际总流量']) / row['实际总流量']
                if row['实际总流量'] > 0 else 0, axis=1
            )

            # 更新备注
            result_df['备注'] = result_df['小时偏差率'].apply(
                lambda x: '>15%' if x > 0.15 else '正常'
            )

            print(f"深度学习模型预测完成，共预测 {len(result_df)} 个时段")
            return result_df

        except Exception as e:
            print(f"深度学习预测失败，使用传统方法: {e}")
            return self._predict_with_traditional_method(target_date)

    def _calculate_planned_traffic(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算基于计划时间的每小时流量"""
        traffic_data = []

        # 检查数据格式，如果是新格式（小时统计数据），直接使用
        if '计划离港数' in df.columns and '计划到港数' in df.columns:
            # 新格式：直接使用统计数据
            for _, row in df.iterrows():
                if 'time' in row.index:
                    hour = pd.to_datetime(row['time']).hour
                else:
                    hour = 0  # 默认值

                traffic_data.append({
                    'hour': hour,
                    'planned_inbound': int(row.get('计划到港数', 0)),
                    'planned_outbound': int(row.get('计划离港数', 0)),
                    'planned_total': int(row.get('计划到港数', 0)) + int(row.get('计划离港数', 0))
                })
        else:
            # 旧格式：按小时统计
            for hour in range(7, 24):  # 7:00-23:59
                hour_str = f"{hour:02d}"

                # 计划进港流量（到达ZGGG的航班）
                planned_arrivals = df[
                    (df.get('planned_arr_airport', '') == 'ZGGG') &
                    (df.get('planned_arr_time', '').astype(str).str.startswith(hour_str))
                ]

                # 计划出港流量（从ZGGG出发的航班）
                planned_departures = df[
                    (df.get('planned_dep_airport', '') == 'ZGGG') &
                    (df.get('planned_dep_time', '').astype(str).str.startswith(hour_str))
                ]

                traffic_data.append({
                    'hour': hour,
                    'planned_inbound': len(planned_arrivals),
                    'planned_outbound': len(planned_departures),
                    'planned_total': len(planned_arrivals) + len(planned_departures)
                })

        return pd.DataFrame(traffic_data)

    def _calculate_actual_traffic(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算基于实际时间的每小时流量"""
        traffic_data = []

        # 检查数据格式，如果是新格式（小时统计数据），直接使用
        if '实际离港数' in df.columns and '实际到港数' in df.columns:
            # 新格式：直接使用统计数据
            for _, row in df.iterrows():
                if 'time' in row.index:
                    hour = pd.to_datetime(row['time']).hour
                else:
                    hour = 0  # 默认值

                traffic_data.append({
                    'hour': hour,
                    'actual_inbound': int(row.get('实际到港数', 0)),
                    'actual_outbound': int(row.get('实际离港数', 0)),
                    'actual_total': int(row.get('实际到港数', 0)) + int(row.get('实际离港数', 0))
                })
        else:
            # 旧格式：按小时统计
            for hour in range(7, 24):  # 7:00-23:59
                hour_str = f"{hour:02d}"

                # 实际进港流量（到达ZGGG的航班）
                actual_arrivals = df[
                    (df.get('actual_arr_airport', '') == 'ZGGG') &
                    (df.get('actual_arr_time', '').astype(str).str.startswith(hour_str))
                ]

                # 实际出港流量（从ZGGG出发的航班）
                actual_departures = df[
                    (df.get('actual_dep_airport', '') == 'ZGGG') &
                    (df.get('actual_dep_time', '').astype(str).str.startswith(hour_str))
                ]

                traffic_data.append({
                    'hour': hour,
                    'actual_inbound': len(actual_arrivals),
                    'actual_outbound': len(actual_departures),
                    'actual_total': len(actual_arrivals) + len(actual_departures)
                })

        return pd.DataFrame(traffic_data)

    def _merge_and_calculate_deviation(self, planned_df: pd.DataFrame, actual_df: pd.DataFrame) -> pd.DataFrame:
        """合并计划和实际数据，计算偏差率"""
        # 合并数据
        result_df = planned_df.merge(actual_df, on='hour', how='outer')

        # 计算偏差率
        result_df['deviation_rate'] = result_df.apply(
            lambda row: abs(row['planned_total'] - row['actual_total']) / row['actual_total']
            if row['actual_total'] > 0 else 0, axis=1
        )

        # 添加备注列
        result_df['remark'] = result_df['deviation_rate'].apply(
            lambda x: '>15%' if x > 0.15 else '正常'
        )

        # 格式化时段列
        result_df['time_period'] = result_df['hour'].apply(lambda x: f"{x:02d}:00-{x:02d}:59")

        # 重新排列列顺序
        result_df = result_df[[
            'time_period', 'planned_inbound', 'planned_outbound', 'planned_total',
            'actual_inbound', 'actual_outbound', 'actual_total', 'deviation_rate', 'remark'
        ]]

        # 重命名列
        result_df.columns = [
            '时段', '预测进港量', '预测出港量', '预测总流量',
            '实际进港量', '实际出港量', '实际总流量', '小时偏差率', '备注'
        ]

        return result_df
    
    def get_traffic_summary(self, traffic_df: pd.DataFrame) -> Dict:
        """
        获取流量预测汇总统计

        Args:
            traffic_df: 流量预测结果DataFrame

        Returns:
            Dict: 汇总统计信息
        """
        summary = {
            'total_planned_flights': traffic_df['预测总流量'].sum(),
            'total_actual_flights': traffic_df['实际总流量'].sum(),
            'average_deviation_rate': traffic_df['小时偏差率'].mean(),
            'max_deviation_rate': traffic_df['小时偏差率'].max(),
            'over_15_percent_count': len(traffic_df[traffic_df['小时偏差率'] > 0.15]),
            'peak_hour_planned': traffic_df.loc[traffic_df['预测总流量'].idxmax(), '时段'],
            'peak_hour_actual': traffic_df.loc[traffic_df['实际总流量'].idxmax(), '时段'],
            'meets_criteria': (
                traffic_df['小时偏差率'].mean() <= 0.1 and
                len(traffic_df[traffic_df['小时偏差率'] > 0.15]) <= 2
            )
        }

        return summary

class FlightOperationSimulator:
    """航班运行状态推演器"""

    def __init__(self):
        self.capacity_per_hour = 20  # 每小时容量
        self.delay_threshold = 10    # 积压阈值（航班数）

    def simulate_24h_operations(self, hourly_traffic: Dict, disruptions: List = None) -> Dict:
        """
        模拟24小时航班运行状态

        Args:
            hourly_traffic: 小时流量预测
            disruptions: 干扰事件列表 [{'start_hour': int, 'end_hour': int, 'type': str}]

        Returns:
            Dict: 运行状态推演结果
        """
        results = {
            'hourly_status': {},
            'backlog_periods': [],
            'peak_backlog': {'hour': 0, 'count': 0},
            'last_operation_hour': 23
        }

        current_backlog = 0
        backlog_start = None

        for hour in range(24):
            # 获取当前小时的计划流量
            planned_flights = hourly_traffic.get(hour, {'total': 0})['total']

            # 检查是否有干扰
            capacity = self.capacity_per_hour
            if disruptions:
                for disruption in disruptions:
                    if disruption['start_hour'] <= hour <= disruption['end_hour']:
                        if disruption['type'] == 'stop_operations':
                            capacity = 0
                        elif disruption['type'] == 'reduced_capacity':
                            capacity = int(capacity * 0.5)

            # 计算实际处理能力
            total_demand = current_backlog + planned_flights
            processed = min(total_demand, capacity)
            current_backlog = max(0, total_demand - processed)

            # 记录状态
            results['hourly_status'][hour] = {
                'planned_flights': planned_flights,
                'backlog_start': current_backlog,
                'capacity': capacity,
                'processed': processed,
                'backlog_end': current_backlog,
                'is_congested': current_backlog > self.delay_threshold
            }

            # 跟踪积压时段
            if current_backlog > self.delay_threshold:
                if backlog_start is None:
                    backlog_start = hour
            else:
                if backlog_start is not None:
                    results['backlog_periods'].append({
                        'start': backlog_start,
                        'end': hour - 1,
                        'duration': hour - backlog_start
                    })
                    backlog_start = None

            # 更新峰值积压
            if current_backlog > results['peak_backlog']['count']:
                results['peak_backlog'] = {'hour': hour, 'count': current_backlog}

        # 处理最后的积压时段
        if backlog_start is not None:
            results['backlog_periods'].append({
                'start': backlog_start,
                'end': 23,
                'duration': 24 - backlog_start
            })

        # 计算最晚运行时段
        if current_backlog > 0:
            # 估算清理积压需要的额外时间
            extra_hours = int(np.ceil(current_backlog / self.capacity_per_hour))
            results['last_operation_hour'] = min(23 + extra_hours, 30)  # 最多到次日6点

        return results

    def calculate_backlog_deviation(self, predicted_result: Dict, actual_result: Dict) -> Dict:
        """
        计算积压时段偏移误差

        Args:
            predicted_result: 预测推演结果
            actual_result: 实际运行结果

        Returns:
            Dict: 偏移误差统计
        """
        deviation_analysis = {
            'period_deviation': 0,
            'duration_match': False,
            'peak_deviation': 0,
            'last_operation_match': False,
            'meets_criteria': False
        }

        # 分析积压时段偏差
        if predicted_result['backlog_periods'] and actual_result['backlog_periods']:
            pred_period = predicted_result['backlog_periods'][0]
            actual_period = actual_result['backlog_periods'][0]

            # 时段偏差（开始和结束时间）
            start_deviation = abs(pred_period['start'] - actual_period['start'])
            end_deviation = abs(pred_period['end'] - actual_period['end'])
            deviation_analysis['period_deviation'] = max(start_deviation, end_deviation)

            # 持续时长匹配
            deviation_analysis['duration_match'] = pred_period['duration'] == actual_period['duration']

            # 积压峰值偏差
            pred_peak = predicted_result['peak_backlog']['count']
            actual_peak = actual_result['peak_backlog']['count']
            if actual_peak > 0:
                peak_deviation_rate = abs(pred_peak - actual_peak) / actual_peak
                deviation_analysis['peak_deviation'] = peak_deviation_rate

            # 最晚运行时段匹配
            deviation_analysis['last_operation_match'] = (
                predicted_result['last_operation_hour'] == actual_result['last_operation_hour']
            )

            # 综合评估是否满足标准
            deviation_analysis['meets_criteria'] = (
                deviation_analysis['period_deviation'] <= 1 and
                deviation_analysis['duration_match'] and
                deviation_analysis['peak_deviation'] <= 0.15 and
                deviation_analysis['last_operation_match']
            )

        return deviation_analysis

class OptimalDecisionMaker:
    """最优决策方案生成器"""

    def __init__(self):
        self.delay_cost_per_minute = 1.0  # 每分钟延误成本

    def generate_optimal_delay_plan(self, hourly_traffic: Dict, capacity_constraints: Dict) -> Dict:
        """
        生成最优延误决策方案

        Args:
            hourly_traffic: 小时流量数据
            capacity_constraints: 容量约束

        Returns:
            Dict: 最优延误方案
        """
        delay_plan = {
            'total_delay_minutes': 0,
            'affected_flights': [],
            'hourly_adjustments': {}
        }

        current_backlog = 0

        for hour in range(24):
            planned_flights = hourly_traffic.get(hour, {'total': 0})['total']
            capacity = capacity_constraints.get(hour, 20)

            total_demand = current_backlog + planned_flights

            if total_demand > capacity:
                # 需要延误部分航班
                flights_to_delay = total_demand - capacity

                # 简单策略：优先延误后续时段容量较大的航班
                delay_minutes = random.randint(30, 120)

                delay_plan['affected_flights'].extend([
                    {
                        'original_hour': hour,
                        'delay_minutes': delay_minutes,
                        'new_hour': hour + (delay_minutes // 60),
                        'flight_id': f"Flight_{hour}_{i}"
                    }
                    for i in range(flights_to_delay)
                ])

                delay_plan['total_delay_minutes'] += flights_to_delay * delay_minutes
                current_backlog = 0
            else:
                current_backlog = max(0, total_demand - capacity)

            delay_plan['hourly_adjustments'][hour] = {
                'original_demand': total_demand,
                'capacity': capacity,
                'delayed_flights': flights_to_delay if total_demand > capacity else 0
            }

        return delay_plan


class FlightDelayPredictor:
    """航班延误预测器"""

    def __init__(self):
        self.delay_threshold = 15  # 固定延误阈值15分钟
        self.historical_data = None

    def load_data(self, flight_file: str):
        """
        加载航班数据

        Args:
            flight_file: 航班数据文件路径
        """
        self.historical_data = pd.read_excel(flight_file)
        print(f"延误预测器已加载航班数据: {len(self.historical_data)} 条记录")

    def predict_flight_delays(self, target_date: str) -> pd.DataFrame:
        """
        预测指定日期每个航班的延误情况

        Args:
            target_date: 目标日期 (YYYY-MM-DD)

        Returns:
            DataFrame: 包含每个航班延误预测的数据表
        """
        if self.historical_data is None:
            raise ValueError("请先加载航班数据")

        # 筛选目标日期的出港航班数据
        df = self.historical_data[
            (self.historical_data['date'] == target_date) &
            (self.historical_data['planned_dep_airport'] == 'ZGGG')  # 只预测出港航班
        ].copy()

        if df.empty:
            raise ValueError(f"未找到日期 {target_date} 的出港航班数据")

        # 为每个航班预测延误
        predictions = []

        for _, flight in df.iterrows():
            # 简单随机预测延误值：-50到100分钟
            predicted_delay = self._predict_single_flight_delay(flight)

            # 计算预测离港时间
            predicted_dep_time = self._calculate_predicted_time(
                flight['planned_dep_time'], predicted_delay
            )

            predictions.append({
                '航班号': flight['flight_no'],
                '日期': flight['date'],
                '计划离港时间': flight['planned_dep_time'],
                '实际离港时间': flight['actual_dep_time'],
                '预测离港延误(分钟)': predicted_delay,
                '预测离港时间': predicted_dep_time
            })

        return pd.DataFrame(predictions)

    def _predict_single_flight_delay(self, flight_data: pd.Series) -> int:
        """
        预测单个航班的延误时间

        Args:
            flight_data: 航班数据行

        Returns:
            int: 预测延误时间（分钟，-50到100之间）
        """
        # 设置随机种子，基于航班号确保预测结果一致
        flight_seed = hash(flight_data['flight_no']) % 10000
        np.random.seed(flight_seed)

        # 简单随机预测：-50到100分钟
        delay_minutes = np.random.randint(-50, 101)

        return delay_minutes

    def _calculate_predicted_time(self, planned_time: str, delay_minutes: int) -> str:
        """
        计算预测离港时间

        Args:
            planned_time: 计划离港时间 (HH:MM格式)
            delay_minutes: 延误分钟数

        Returns:
            str: 预测离港时间 (HH:MM格式)
        """
        from datetime import datetime, timedelta

        try:
            # 解析计划时间
            planned_dt = datetime.strptime(planned_time, '%H:%M')

            # 加上延误时间
            predicted_dt = planned_dt + timedelta(minutes=delay_minutes)

            # 返回格式化的时间
            return predicted_dt.strftime('%H:%M')

        except Exception:
            # 如果计算失败，返回原计划时间
            return planned_time
