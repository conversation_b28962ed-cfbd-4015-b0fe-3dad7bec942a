#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度学习模型推理测试脚本
用于测试流量预测算法
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

try:
    from prediction_models import FlightTrafficPredictor
    print("成功导入预测模型")
except ImportError as e:
    print(f"导入预测模型失败: {e}")
    sys.exit(1)

def test_traditional_prediction():
    """测试传统预测算法"""
    print("\n=== 测试传统预测算法 ===")
    
    # 创建预测器（不使用深度学习）
    predictor = FlightTrafficPredictor(use_deep_learning=False)
    
    # 加载数据
    data_path = os.path.join('data', 'ZGGG_小时统计数据集.csv')
    if not os.path.exists(data_path):
        print(f"数据文件不存在: {data_path}")
        return
    
    try:
        predictor.load_data(data_path)
        print(f"成功加载数据: {len(predictor.historical_data)} 条记录")
        
        # 查看数据的日期范围
        if 'time' in predictor.historical_data.columns:
            predictor.historical_data['time'] = pd.to_datetime(predictor.historical_data['time'])
            min_date = predictor.historical_data['time'].min()
            max_date = predictor.historical_data['time'].max()
            print(f"数据日期范围: {min_date} 到 {max_date}")
            
            # 选择一个测试日期
            test_date = min_date.strftime('%Y-%m-%d')
            print(f"使用测试日期: {test_date}")
            
            # 进行预测
            result = predictor.predict_hourly_traffic(test_date)
            print(f"预测结果:")
            print(result.head())
            
            # 显示汇总统计
            summary = predictor.get_traffic_summary(result)
            print(f"\n汇总统计:")
            for key, value in summary.items():
                print(f"  {key}: {value}")
                
        else:
            print("数据中没有找到时间列")
            
    except Exception as e:
        print(f"传统预测测试失败: {e}")

def test_deep_learning_prediction():
    """测试深度学习预测算法"""
    print("\n=== 测试深度学习预测算法 ===")
    
    # 创建预测器（使用深度学习）
    predictor = FlightTrafficPredictor(use_deep_learning=True)
    
    # 加载数据
    data_path = os.path.join('data', 'ZGGG_小时统计数据集.csv')
    if not os.path.exists(data_path):
        print(f"数据文件不存在: {data_path}")
        return
    
    try:
        predictor.load_data(data_path)
        print(f"成功加载数据: {len(predictor.historical_data)} 条记录")
        
        if predictor.use_deep_learning:
            print("深度学习模式已启用")
        else:
            print("深度学习模式未启用，将使用传统方法")
        
        # 查看数据的日期范围
        if 'time' in predictor.historical_data.columns:
            predictor.historical_data['time'] = pd.to_datetime(predictor.historical_data['time'])
            min_date = predictor.historical_data['time'].min()
            max_date = predictor.historical_data['time'].max()
            print(f"数据日期范围: {min_date} 到 {max_date}")
            
            # 选择一个测试日期
            test_date = min_date.strftime('%Y-%m-%d')
            print(f"使用测试日期: {test_date}")
            
            # 进行预测
            result = predictor.predict_hourly_traffic(test_date)
            print(f"预测结果:")
            print(result.head())
            
            # 显示汇总统计
            summary = predictor.get_traffic_summary(result)
            print(f"\n汇总统计:")
            for key, value in summary.items():
                print(f"  {key}: {value}")
                
        else:
            print("数据中没有找到时间列")
            
    except Exception as e:
        print(f"深度学习预测测试失败: {e}")

def main():
    """主函数"""
    print("流量预测算法测试")
    print("=" * 50)
    
    # 检查数据文件
    data_path = os.path.join('data', 'ZGGG_小时统计数据集.csv')
    if not os.path.exists(data_path):
        print(f"错误: 数据文件不存在 {data_path}")
        return
    
    # 检查模型文件
    model_path = os.path.join('model', 'best_transformer_model.pth')
    bert_path = os.path.join('model', 'bert_models')
    resnet_path = os.path.join('model', 'resnet50-0676ba61.pth')
    
    print(f"数据文件: {'✓' if os.path.exists(data_path) else '✗'} {data_path}")
    print(f"模型权重: {'✓' if os.path.exists(model_path) else '✗'} {model_path}")
    print(f"BERT模型: {'✓' if os.path.exists(bert_path) else '✗'} {bert_path}")
    print(f"ResNet权重: {'✓' if os.path.exists(resnet_path) else '✗'} {resnet_path}")
    
    # 测试传统算法
    test_traditional_prediction()
    
    # 测试深度学习算法
    test_deep_learning_prediction()

if __name__ == "__main__":
    main()
