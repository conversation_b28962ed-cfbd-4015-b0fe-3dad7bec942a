import pandas as pd
import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
from torchvision import models, transforms
from transformers import BertTokenizer, BertModel
from sklearn.preprocessing import StandardScaler
from PIL import Image
import os
import numpy as np
from tqdm import tqdm
import math
import warnings
from pandas.errors import SettingWithCopyWarning  # 正确的导入方式

warnings.filterwarnings('ignore', category=SettingWithCopyWarning)


# --- 1. 配置类 (更新) ---
class Config:
    # !!重要!!: 直接指向您已处理好的小时级数据文件
    DATA_CSV_PATH = "/share/home/<USER>/liuliang/ZGGG_小时统计数据集.csv"
    IMAGE_DIR = "/share/home/<USER>/liuliang/guangzhou_gray"
    BERT_MODEL_PATH = os.path.join('/share/home/<USER>/liuliang/bert_models')
    RESNET_WEIGHTS_PATH = os.path.join('/share/home/<USER>/liuliang/resnet50-0676ba61.pth')


    HISTORICAL_DAYS = 7
    # 输入序列长度 = 历史天数 + 当前小时
    INPUT_SEQ_LEN = HISTORICAL_DAYS + 1

    IMG_SIZE = 224
    EMBED_DIM = 512
    NUM_TABULAR_FEATURES = 2 + 16

    # Transformer Encoder 配置
    TRANSFORMER_NHEAD = 8
    TRANSFORMER_ENCODER_LAYERS = 5
    TRANSFORMER_DIM_FEEDFORWARD = 2048

    DROPOUT = 0.1
    BATCH_SIZE = 6  # 由于序列变长，可能需要减小Batch Size以防显存不足
    LEARNING_RATE = 5e-5  # Transformer模型可能对稍小的学习率更敏感
    EPOCHS = 50
    DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    OUTPUT_DIR = '/share/home/<USER>/eval-7'


cfg = Config()


# --- 2. 数据集类 (重构以处理特征序列) ---
class FlightDataset(Dataset):
    def __init__(self, cfg, tokenizer):
        self.cfg = cfg
        try:
            df = pd.read_csv(cfg.DATA_CSV_PATH, parse_dates=['time'])
        except FileNotFoundError:
            raise FileNotFoundError(f"数据文件未找到: {cfg.DATA_CSV_PATH}。请确保该文件存在且路径正确。")

        self.df = df.copy()
        self.tokenizer = tokenizer

        self.df.set_index('time', inplace=True)

        self.tabular_cols = ['计划离港数', '计划到港数'] + [f'方向_{d}' for d in
                                                            ['N', 'NNE', 'NE', 'ENE', 'E', 'ESE', 'SE', 'SSE', 'S',
                                                             'SSW', 'SW', 'WSW', 'W', 'WNW', 'NW', 'NNW']]
        self.target_cols = ['实际离港数', '实际到港数']

        # 对数值特征和目标进行标准化
        self.tabular_scaler = StandardScaler()
        self.df[self.tabular_cols] = self.tabular_scaler.fit_transform(self.df[self.tabular_cols])

        self.target_scaler = StandardScaler()
        self.df[self.target_cols] = self.target_scaler.fit_transform(self.df[self.target_cols])

        self.image_transform = transforms.Compose([
            transforms.Resize((cfg.IMG_SIZE, cfg.IMG_SIZE)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
        ])

        self.index_to_time = self.df.index

    def __len__(self):
        # 确保有足够长的历史数据 (5天 * 24小时)
        return len(self.df) - self.cfg.HISTORICAL_DAYS * 24

    def __getitem__(self, idx):
        # 目标索引：我们要预测的那个小时
        target_time_idx = idx + self.cfg.HISTORICAL_DAYS * 24
        target_time = self.index_to_time[target_time_idx]

        # --- 获取输入特征序列 (过去5天 + 当前) ---
        input_timestamps = [target_time - pd.Timedelta(days=d) for d in range(self.cfg.HISTORICAL_DAYS, -1, -1)]

        all_tabular_data, all_images, all_texts = [], [], []

        for ts in input_timestamps:
            if ts in self.df.index:
                row = self.df.loc[ts]
            else:
                # 如果缺少历史数据，用0填充所有特征
                row = pd.Series(0, index=self.df.columns)

            # 1. 数值特征
            all_tabular_data.append(torch.tensor(row[self.tabular_cols].values.astype(np.float32), dtype=torch.float32))

            # 2. 图像特征 (12个雷达图)
            images_for_ts = []
            for i in range(1, 13):
                img_path = row.get(f'雷达图{i}')
                if pd.isna(img_path) or not os.path.exists(str(img_path)):
                    images_for_ts.append(torch.zeros((3, self.cfg.IMG_SIZE, self.cfg.IMG_SIZE)))
                else:
                    try:
                        img = Image.open(img_path).convert('RGB')
                        images_for_ts.append(self.image_transform(img))
                    except Exception:
                        images_for_ts.append(torch.zeros((3, self.cfg.IMG_SIZE, self.cfg.IMG_SIZE)))
            all_images.append(torch.stack(images_for_ts))

            # 3. 文本特征 (4+4+12 = 20个文本)
            texts_for_ts = [str(row.get(f'天气报文{i}', '')) for i in range(1, 5)] + \
                           [str(row.get(f'天气预报{i}', '')) for i in range(1, 5)] + \
                           [str(row.get(f'雷达报告{i}', '')) for i in range(1, 13)]
            all_texts.extend(texts_for_ts)

        # --- 统一处理和堆叠 ---
        tabular_data_seq = torch.stack(all_tabular_data)  # [SeqLen, NumTabular]
        image_data_seq = torch.stack(all_images)  # [SeqLen, 12, 3, H, W]

        # 统一编码所有文本
        text_tokens = self.tokenizer(all_texts, padding='max_length', truncation=True, max_length=64,
                                     return_tensors='pt')
        # 重塑为序列格式
        num_texts_per_step = 20
        text_ids_seq = text_tokens['input_ids'].view(self.cfg.INPUT_SEQ_LEN, num_texts_per_step, -1)
        text_mask_seq = text_tokens['attention_mask'].view(self.cfg.INPUT_SEQ_LEN, num_texts_per_step, -1)

        # --- 获取目标 ---
        target_row = self.df.loc[target_time]
        target = torch.tensor(target_row[self.target_cols].values.astype(np.float32), dtype=torch.float32)

        return {
            'tabular_input': tabular_data_seq,  # [6, 18]
            'image_input': image_data_seq,  # [6, 12, 3, H, W]
            'text_input_ids': text_ids_seq,  # [6, 20, 64]
            'text_attention_mask': text_mask_seq,  # [6, 20, 64]
            'target': target,  # [2]
            'time_info': str(target_time)
        }


# --- 3. 模型架构 (重构为Transformer Encoder模型) ---
class PositionalEncoding(nn.Module):
    def __init__(self, d_model, dropout=0.1, max_len=5000):
        super(PositionalEncoding, self).__init__()
        self.dropout = nn.Dropout(p=dropout)
        position = torch.arange(max_len).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2) * (-math.log(10000.0) / d_model))
        pe = torch.zeros(max_len, 1, d_model)
        pe[:, 0, 0::2] = torch.sin(position * div_term)
        pe[:, 0, 1::2] = torch.cos(position * div_term)
        self.register_buffer('pe', pe)

    def forward(self, x):
        x = x + self.pe[:x.size(0)]
        return self.dropout(x)


class FlightTrafficModel(nn.Module):
    def __init__(self, cfg, bert_model):
        super(FlightTrafficModel, self).__init__()
        self.cfg = cfg

        # -- 特征提取器 (与之前类似，但用于单步) --
        resnet = models.resnet50(weights=None)
        try:
            resnet.load_state_dict(torch.load(cfg.RESNET_WEIGHTS_PATH))
        except FileNotFoundError:
            raise FileNotFoundError(f"ResNet50权重文件未找到: {cfg.RESNET_WEIGHTS_PATH}.")
        self.image_feature_extractor = nn.Sequential(*list(resnet.children())[:-1])
        self.image_aggregator = nn.GRU(input_size=2048, hidden_size=cfg.EMBED_DIM, batch_first=True)

        self.text_feature_extractor = bert_model
        self.text_aggregator = nn.GRU(input_size=bert_model.config.hidden_size, hidden_size=cfg.EMBED_DIM,
                                      batch_first=True)

        self.tabular_projector = nn.Linear(cfg.NUM_TABULAR_FEATURES, cfg.EMBED_DIM)

        # -- 融合层，将单步的三种模态融合成一个向量 --
        self.fusion_projector = nn.Linear(cfg.EMBED_DIM * 3, cfg.EMBED_DIM)

        # -- Transformer Encoder 模块 --
        self.pos_encoder = PositionalEncoding(cfg.EMBED_DIM, cfg.DROPOUT)
        encoder_layer = nn.TransformerEncoderLayer(d_model=cfg.EMBED_DIM, nhead=cfg.TRANSFORMER_NHEAD,
                                                   dim_feedforward=cfg.TRANSFORMER_DIM_FEEDFORWARD,
                                                   dropout=cfg.DROPOUT, batch_first=True)
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers=cfg.TRANSFORMER_ENCODER_LAYERS)

        # -- 预测头 --
        self.prediction_head = nn.Sequential(
            nn.LayerNorm(cfg.EMBED_DIM),
            nn.Linear(cfg.EMBED_DIM, cfg.EMBED_DIM // 2),
            nn.ReLU(),
            nn.Dropout(cfg.DROPOUT),
            nn.Linear(cfg.EMBED_DIM // 2, 2)
        )

    def forward(self, batch):
        B, S, *_ = batch['tabular_input'].shape  # Batch_size, Seq_len=6

        fused_features_seq = []
        # 对序列中的每个时间步进行特征提取和融合
        for t in range(S):
            # 1. 图像
            images_t = batch['image_input'][:, t, :, :, :, :]  # [B, 12, C, H, W]
            N_imgs = images_t.shape[1]
            img_flat = images_t.reshape(B * N_imgs, *images_t.shape[2:])
            img_feats = self.image_feature_extractor(img_flat).view(B, N_imgs, -1)
            _, img_embedding = self.image_aggregator(img_feats)
            img_embedding = img_embedding.squeeze(0)

            # 2. 文本
            text_ids_t = batch['text_input_ids'][:, t, :, :]  # [B, 20, 64]
            text_mask_t = batch['text_attention_mask'][:, t, :, :]
            N_texts = text_ids_t.shape[1]
            text_ids_flat = text_ids_t.reshape(B * N_texts, -1)
            text_mask_flat = text_mask_t.reshape(B * N_texts, -1)
            text_outputs = self.text_feature_extractor(input_ids=text_ids_flat, attention_mask=text_mask_flat)
            text_cls_feats = text_outputs.last_hidden_state[:, 0, :].view(B, N_texts, -1)
            _, text_embedding = self.text_aggregator(text_cls_feats)
            text_embedding = text_embedding.squeeze(0)

            # 3. 表格
            tabular_input_t = batch['tabular_input'][:, t, :]
            tabular_embedding = self.tabular_projector(tabular_input_t)

            # 融合单步特征
            fused_t = torch.cat([img_embedding, text_embedding, tabular_embedding], dim=1)
            fused_features_seq.append(self.fusion_projector(fused_t))

        # 将序列的特征堆叠起来
        encoder_input = torch.stack(fused_features_seq, dim=1)  # [B, SeqLen, EmbedDim]

        # 添加位置编码并送入Transformer Encoder
        encoder_input = self.pos_encoder(encoder_input.permute(1, 0, 2)).permute(1, 0, 2)  # PE需要 [S, B, E]
        encoder_output = self.transformer_encoder(encoder_input)  # [B, SeqLen, EmbedDim]

        # 使用序列最后一个时间步的输出进行预测
        # 这个向量包含了历史信息的上下文
        final_vector = encoder_output[:, -1, :]  # [B, EmbedDim]

        prediction = self.prediction_head(final_vector)

        return prediction


# --- 4. 评估与训练函数 (与之前版本基本一致) ---
# calculate_metrics, train_epoch, eval_epoch 函数无需修改，此处省略以节约篇幅
# 您可以从我上一个回答中直接复制过来

def calculate_metrics(outputs, targets):
    loss_fn = nn.MSELoss()
    mae_fn = nn.L1Loss()

    loss = loss_fn(outputs, targets)
    mae = mae_fn(outputs, targets)

    return {"loss": loss, "mae": mae}


def train_epoch(model, dataloader, optimizer, device):
    model.train()
    total_loss, total_mae = 0, 0
    for batch in tqdm(dataloader, desc="Training"):
        optimizer.zero_grad()

        for k, v in batch.items():
            if k != 'time_info':
                batch[k] = v.to(device)

        outputs = model(batch)
        metrics = calculate_metrics(outputs, batch['target'])

        loss = metrics["loss"]
        loss.backward()
        optimizer.step()

        total_loss += loss.item()
        total_mae += metrics["mae"].item()

    num_batches = len(dataloader)
    return {'loss': total_loss / num_batches, 'mae': total_mae / num_batches}


def eval_epoch(model, dataloader, device, target_scaler):
    model.eval()
    total_loss, total_mae = 0, 0
    all_preds, all_targets, all_time_info = [], [], []

    with torch.no_grad():
        for batch in tqdm(dataloader, desc="Evaluating"):
            all_time_info.extend(batch['time_info'])
            for k, v in batch.items():
                if k != 'time_info':
                    batch[k] = v.to(device)

            outputs = model(batch)
            metrics = calculate_metrics(outputs, batch['target'])

            total_loss += metrics["loss"].item()
            total_mae += metrics["mae"].item()

            preds_unscaled = target_scaler.inverse_transform(outputs.cpu().numpy())
            targets_unscaled = target_scaler.inverse_transform(batch['target'].cpu().numpy())

            all_preds.append(preds_unscaled)
            all_targets.append(targets_unscaled)

    num_batches = len(dataloader)
    avg_metrics = {'loss': total_loss / num_batches, 'mae': total_mae / num_batches}

    preds_np = np.concatenate(all_preds, axis=0)
    targets_np = np.concatenate(all_targets, axis=0)

    df_results = pd.DataFrame({
        "time": all_time_info,
        "pred_depart": preds_np[:, 0],
        "true_depart": targets_np[:, 0],
        "pred_arrive": preds_np[:, 1],
        "true_arrive": targets_np[:, 1],
    })

    return avg_metrics, df_results


# --- 5. 繁忙时段性能评估函数 (不变) ---
# evaluate_busy_hours_performance 函数无需修改，此处省略以节约篇幅
def evaluate_busy_hours_performance(df_results):
    """根据特定业务规则评估模型性能"""
    print("\n--- 繁忙时段性能评估 ---")

    df = df_results.copy()
    df['time'] = pd.to_datetime(df['time'])

    # 规则1: 筛选繁忙时段 (7:00 至 23:00, 即小时属于 [7, 22])
    busy_hours_df = df[df['time'].dt.hour.isin(range(7, 23))].copy()
    print(f"总预测时段: {len(df)}, 其中繁忙时段: {len(busy_hours_df)}")

    # 规则2: 排除临时停止起降的时段 (小时实际进出港总数 <= 2)
    busy_hours_df['actual_total'] = busy_hours_df['true_depart'] + busy_hours_df['true_arrive']
    eval_df = busy_hours_df[busy_hours_df['actual_total'] > 2].copy()
    print(f"从繁忙时段中排除 {len(busy_hours_df) - len(eval_df)} 个临时停航时段。最终用于评估的时段数: {len(eval_df)}")

    if len(eval_df) == 0:
        print("没有可用于评估的繁忙时段。")
        return

    # 计算每个时段的进出港总流量和偏差率
    eval_df['pred_total'] = eval_df['pred_depart'] + eval_df['pred_arrive']
    # 增加一个极小值避免除以零
    eval_df['deviation_rate'] = np.abs(eval_df['pred_total'] - eval_df['actual_total']) / (
            eval_df['actual_total'] + 1e-6)

    # 评估指标
    avg_deviation_rate = eval_df['deviation_rate'].mean()
    exceed_15_percent_count = (eval_df['deviation_rate'] > 0.15).sum()

    print("\n--- 评估结果 ---")
    print(f"1. 平均偏差率: {avg_deviation_rate:.2%}")
    print(f"2. 偏差率超过15%的时段数: {exceed_15_percent_count} (总评估时段数: {len(eval_df)})")

    # 判断是否达标
    is_avg_rate_ok = avg_deviation_rate <= 0.10
    is_exceed_count_ok = exceed_15_percent_count <= 2

    print("\n--- 结论 ---")
    if is_avg_rate_ok:
        print("✅ 平均偏差率控制在10%以内，【达标】。")
    else:
        print(f"❌ 平均偏差率未达到10%的要求，【不达标】。")

    if is_exceed_count_ok:
        print("✅ 超过15%偏差率的时段数不多于2个，【达标】。")
    else:
        print(f"❌ 超过15%偏差率的时段数大于2个，【不达标】。")




'''
# --- 6. 主执行流程 (更新) ---
if __name__ == '__main__':
    # 步骤1: 初始化模型和分词器 (无需数据预处理)
    print(f"使用的设备: {cfg.DEVICE}")
    try:
        tokenizer = BertTokenizer.from_pretrained(cfg.BERT_MODEL_PATH)
        bert_model = BertModel.from_pretrained(cfg.BERT_MODEL_PATH)
    except OSError:
        raise OSError(f"BERT模型文件未在 '{cfg.BERT_MODEL_PATH}' 找到。")

    for param in bert_model.parameters():
        param.requires_grad = False
    bert_model.to(cfg.DEVICE)

    # 步骤2: 创建数据集和数据加载器
    full_dataset = FlightDataset(cfg, tokenizer)

    train_size = int(0.8 * len(full_dataset))
    val_size = len(full_dataset) - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(full_dataset, [train_size, val_size])

    train_loader = DataLoader(train_dataset, batch_size=cfg.BATCH_SIZE, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=cfg.BATCH_SIZE, shuffle=False)

    # 步骤3: 初始化新模型、优化器
    model = FlightTrafficModel(cfg, bert_model).to(cfg.DEVICE)
    optimizer = torch.optim.AdamW(filter(lambda p: p.requires_grad, model.parameters()), lr=cfg.LEARNING_RATE)
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, 'min', factor=0.5, patience=3)

    print(f"模型可训练参数量: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")

    # 步骤4: 训练循环
    print("\n开始训练...")
    best_val_loss = float('inf')
    best_val_results_df = None

    for epoch in range(cfg.EPOCHS):
        train_metrics = train_epoch(model, train_loader, optimizer, cfg.DEVICE)
        val_metrics, val_results_df = eval_epoch(model, val_loader, cfg.DEVICE, full_dataset.target_scaler)
        scheduler.step(val_metrics["loss"])

        print(f"\n--- Epoch {epoch + 1}/{cfg.EPOCHS} ---")
        print(f"[Train] MSE Loss: {train_metrics['loss']:.4f} | MAE: {train_metrics['mae']:.4f}")
        print(f"[Valid] MSE Loss: {val_metrics['loss']:.4f} | MAE: {val_metrics['mae']:.4f}")
        print("-" * 60)

        if val_metrics['loss'] < best_val_loss:
            best_val_loss = val_metrics['loss']
            best_val_results_df = val_results_df
            model_save_path = os.path.join(cfg.OUTPUT_DIR, "best_transformer_model.pth")
            torch.save(model.state_dict(), model_save_path)
            print(f"验证集损失创新低，模型已保存到: {model_save_path}")

    print("训练完成!")

    # 步骤5: 使用最佳模型在验证集上的结果进行最终评估
    if best_val_results_df is not None:
        save_csv_path = os.path.join(cfg.OUTPUT_DIR, "best_transformer_validation_predictions.csv")
        best_val_results_df.to_csv(save_csv_path, index=False)
        print(f"\n最佳验证集预测结果已保存到: {save_csv_path}")

        evaluate_busy_hours_performance(best_val_results_df)
'''

# --- 6. 推理模式函数 ---
def run_inference(cfg, model, dataset, start_date=None, end_date=None):
    """
    使用加载好的模型对指定日期范围的数据进行推理。

    Args:
        cfg (Config): 配置对象。
        model (nn.Module): 已加载权重的模型。
        dataset (FlightDataset): 完整的数据集实例，用于数据预处理和逆标准化。
        start_date (str, optional): 推理开始日期，格式 'YYYY-MM-DD HH:MM:SS'。默认为None，从最早可预测的时间点开始。
        end_date (str, optional): 推理结束日期，格式 'YYYY-MM-DD HH:MM:SS'。默认为None，到最晚可预测的时间点结束。

    Returns:
        pd.DataFrame: 包含时间和预测/真实流量的DataFrame。
    """
    print("\n--- 进入推理模式 ---")
    model.to(cfg.DEVICE)
    model.eval()

    # --- 确定推理的数据子集 ---
    full_indices = list(range(len(dataset)))
    start_idx, end_idx = 0, len(dataset) - 1

    # `dataset` 的第0个样本对应于原始数据中第 `HISTORICAL_DAYS * 24` 个小时
    first_predictable_time = dataset.index_to_time[cfg.HISTORICAL_DAYS * 24]
    last_predictable_time = dataset.index_to_time[-1]

    if start_date:
        start_time = pd.to_datetime(start_date)
        if start_time < first_predictable_time:
            print(f"警告: 开始日期 {start_date} 早于最早可预测的日期 {first_predictable_time}。将从最早日期开始。")
            start_idx = 0
        else:
            # 找到对应于 start_time 的数据集索引
            time_diff = (start_time - first_predictable_time).total_seconds() / 3600
            start_idx = int(time_diff)

    if end_date:
        end_time = pd.to_datetime(end_date)
        if end_time > last_predictable_time:
            print(f"警告: 结束日期 {end_date} 晚于最晚可预测的日期 {last_predictable_time}。将到最晚日期结束。")
            end_idx = len(dataset) - 1
        else:
            time_diff = (end_time - first_predictable_time).total_seconds() / 3600
            end_idx = int(time_diff)

    if start_idx > end_idx:
        print("错误: 开始日期晚于结束日期，无法进行推理。")
        return pd.DataFrame()

    inference_indices = full_indices[start_idx:end_idx + 1]
    inference_subset = torch.utils.data.Subset(dataset, inference_indices)

    if not inference_subset:
        print("错误: 在指定日期范围内未找到可用于推理的数据。")
        return pd.DataFrame()

    print(f"将对 {len(inference_subset)} 个时段进行推理。")
    print(f"从: {dataset.index_to_time[inference_indices[0] + cfg.HISTORICAL_DAYS * 24]}")
    print(f"至: {dataset.index_to_time[inference_indices[-1] + cfg.HISTORICAL_DAYS * 24]}")

    inference_loader = DataLoader(inference_subset, batch_size=cfg.BATCH_SIZE, shuffle=False)

    all_preds, all_targets, all_time_info = [], [], []

    with torch.no_grad():
        for batch in tqdm(inference_loader, desc="推理中"):
            all_time_info.extend(batch['time_info'])
            for k, v in batch.items():
                if k != 'time_info':
                    batch[k] = v.to(cfg.DEVICE)

            outputs = model(batch)

            # 使用数据集的scaler进行逆标准化
            preds_unscaled = dataset.target_scaler.inverse_transform(outputs.cpu().numpy())
            targets_unscaled = dataset.target_scaler.inverse_transform(batch['target'].cpu().numpy())

            all_preds.append(preds_unscaled)
            all_targets.append(targets_unscaled)

    preds_np = np.concatenate(all_preds, axis=0)
    targets_np = np.concatenate(all_targets, axis=0)

    df_results = pd.DataFrame({
        "time": all_time_info,
        "pred_depart": preds_np[:, 0],
        "true_depart": targets_np[:, 0],
        "pred_arrive": preds_np[:, 1],
        "true_arrive": targets_np[:, 1],
    })

    # 对结果进行评估
    evaluate_busy_hours_performance(df_results)

    return df_results

### **更新主执行块 (`if __name__ == '__main__':`)**
if __name__ == '__main__':
    # --- 配置 ---
    # 将 INFERENCE_ONLY 设置为 True 以跳过训练，直接进行推理
    INFERENCE_ONLY = True
    # 指定推理日期范围 (可选)。如果设为 None，则对整个数据集进行推理。
    # INFERENCE_START_DATE = "2025-05-20 00:00:00"
    # INFERENCE_END_DATE = "2025-05-25 23:00:00"
    INFERENCE_START_DATE = None
    INFERENCE_END_DATE = None

    cfg = Config()
    print(f"使用的设备: {cfg.DEVICE}")

    # --- 初始化 ---
    try:
        tokenizer = BertTokenizer.from_pretrained(cfg.BERT_MODEL_PATH)
        bert_model = BertModel.from_pretrained(cfg.BERT_MODEL_PATH)
    except OSError:
        raise OSError(f"BERT模型文件未在 '{cfg.BERT_MODEL_PATH}' 找到。")

    # 冻结BERT参数
    for param in bert_model.parameters():
        param.requires_grad = False
    bert_model.to(cfg.DEVICE)

    # 完整数据集（对于获取scaler和时间索引至关重要）
    full_dataset = FlightDataset(cfg, tokenizer)

    # --- 实例化模型 ---
    model = FlightTrafficModel(cfg, bert_model).to(cfg.DEVICE)

    if INFERENCE_ONLY:
        # --- 推理模式 ---
        model_path = os.path.join(cfg.OUTPUT_DIR, "best_transformer_model.pth")
        try:
            model.load_state_dict(torch.load(model_path, map_location=cfg.DEVICE))
            print(f"成功从 {model_path} 加载模型权重。")
        except FileNotFoundError:
            raise FileNotFoundError(f"推理失败：找不到模型权重文件 at {model_path}")

        # 运行推理
        inference_results_df = run_inference(cfg, model, full_dataset,
                                             start_date=INFERENCE_START_DATE,
                                             end_date=INFERENCE_END_DATE)

        # 保存推理结果
        if not inference_results_df.empty:
            inference_save_path = os.path.join(cfg.OUTPUT_DIR, "inference_predictions.csv")
            inference_results_df.to_csv(inference_save_path, index=False)
            print(f"\n推理结果已保存到: {inference_save_path}")
            print("\n推理结果预览:")
            print(inference_results_df.head())

    else:
        # --- 训练模式 (原始代码) ---
        train_size = int(0.8 * len(full_dataset))
        val_size = len(full_dataset) - train_size
        train_dataset, val_dataset = torch.utils.data.random_split(full_dataset, [train_size, val_size])

        train_loader = DataLoader(train_dataset, batch_size=cfg.BATCH_SIZE, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=cfg.BATCH_SIZE, shuffle=False)

        optimizer = torch.optim.AdamW(filter(lambda p: p.requires_grad, model.parameters()), lr=cfg.LEARNING_RATE)
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, 'min', factor=0.5, patience=3)

        print(f"模型可训练参数量: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")

        # ... (此处省略您的原始训练循环代码) ...
        print("\n开始训练...")
        best_val_loss = float('inf')
        best_val_results_df = None

        for epoch in range(cfg.EPOCHS):
            train_metrics = train_epoch(model, train_loader, optimizer, cfg.DEVICE)
            val_metrics, val_results_df = eval_epoch(model, val_loader, cfg.DEVICE, full_dataset.target_scaler)
            scheduler.step(val_metrics["loss"])

            print(f"\n--- Epoch {epoch + 1}/{cfg.EPOCHS} ---")
            print(f"[Train] MSE Loss: {train_metrics['loss']:.4f} | MAE: {train_metrics['mae']:.4f}")
            print(f"[Valid] MSE Loss: {val_metrics['loss']:.4f} | MAE: {val_metrics['mae']:.4f}")
            print("-" * 60)

            if val_metrics['loss'] < best_val_loss:
                best_val_loss = val_metrics['loss']
                best_val_results_df = val_results_df
                model_save_path = os.path.join(cfg.OUTPUT_DIR, "best_transformer_model.pth")
                torch.save(model.state_dict(), model_save_path)
                print(f"验证集损失创新低，模型已保存到: {model_save_path}")

        print("训练完成!")

        # ... (此处省略您的原始评估代码) ...
        if best_val_results_df is not None:
            save_csv_path = os.path.join(cfg.OUTPUT_DIR, "best_transformer_validation_predictions.csv")
            best_val_results_df.to_csv(save_csv_path, index=False)
            print(f"\n最佳验证集预测结果已保存到: {save_csv_path}")
            evaluate_busy_hours_performance(best_val_results_df)