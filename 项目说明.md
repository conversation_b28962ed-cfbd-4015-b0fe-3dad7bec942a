# 挑战杯航班流量预测与运行状态推演系统

## 项目概述

本项目是为挑战杯竞赛开发的航班流量预测与运行状态推演系统，专门针对广州白云机场的航班运行管理需求。系统实现了基于天气预报的流量预测、24小时运行状态推演、停止起降情景分析等核心功能。

## 系统架构

### 技术栈
- **前端界面**: PyQt5 - 现代化的桌面应用程序界面
- **数据处理**: Pandas + NumPy - 高效的数据分析和处理
- **可视化**: Matplotlib + Seaborn - 专业的图表展示
- **数据存储**: Excel (xlsx) - 便于数据交换和查看
- **打包部署**: PyInstaller - 生成独立的exe可执行文件

### 模块结构
```
pyqt_demo/
├── main.py                 # 主程序入口
├── requirements.txt        # 依赖包列表
├── build_simple.py           # 打包脚本
├── src/                   # 源代码目录
│   ├── __init__.py
│   ├── main_window.py     # 主窗口界面
│   ├── prediction_models.py  # 预测算法模块
│   ├── simulation_controller.py  # 推演控制器
│   ├── visualization.py   # 数据可视化模块
│   └── data_generator.py  # 数据生成器
├── data/                  # 数据文件目录
│   ├── 广州白云机场航班时刻表.xlsx
│   └── 广州白云机场天气数据.xlsx
├── resources/             # 资源文件目录
└── docs/                  # 文档目录
```

## 核心功能

### 1. 流量预测模型 (指标1)
- **功能**: 基于天气预报的航空公司流量预测
- **指标**: 机场各小时预测流量偏差率
- **要求**: 16个小时平均偏差率≤10%，最多2个时段偏差率>15%
- **实现**: 随机预测算法，考虑天气因素影响

### 2. 运行状态推演模型 (指标2)
- **功能**: 未来24小时航班运行状态推演
- **指标**: 出港积压发生时段偏移误差
- **要求**: 积压时段、持续时长、峰值积压、最晚运行时段准确预测
- **实现**: 基于容量约束的排队论模型

### 3. 停止起降情景推演 (指标3)
- **功能**: 停止起降情景下出港积压化解分析
- **指标**: 停止起降情景下出港积压化解偏移误差
- **要求**: 手动设定停止起降时段，推演积压化解过程
- **实现**: 情景模拟算法

### 4. 最优决策方案生成
- **功能**: 基于总体延误量最少的目标生成决策方案
- **输出**: 具体延误哪些航班的优化方案
- **实现**: 启发式优化算法

## 界面设计

### 主界面特点
- **现代化设计**: 采用扁平化设计风格，界面简洁美观
- **选项卡布局**: 功能模块清晰分离，操作流程直观
- **实时反馈**: 进度条、状态栏提供实时操作反馈
- **图表展示**: 多种图表类型，数据可视化效果良好

### 功能模块
1. **数据管理**: 航班数据和天气数据的加载和预览
2. **流量预测**: 预测参数设置和预测结果展示
3. **运行推演**: 推演参数配置和实时推演控制
4. **结果展示**: 多维度图表展示和详细分析结果

## 数据模拟

### 航班数据
- **时间范围**: 一个月的航班计划数据
- **数据量**: 每天200-300个航班，总计约8000条记录
- **字段**: 航班号、时间、机型、目的地、延误情况等
- **特点**: 符合真实机场运行规律，繁忙时段集中在7-23点

### 天气数据
- **时间范围**: 对应航班数据的天气信息
- **频率**: 每小时一条记录
- **字段**: 温度、湿度、风速、能见度、天气状况等
- **用途**: 影响流量预测的准确性

## 性能指标

### 运算效率
- **预测计算**: 单次预测耗时 < 1秒
- **推演计算**: 24小时推演耗时 < 3分钟
- **内存占用**: 运行时内存占用 < 500MB
- **响应速度**: 界面操作响应时间 < 100ms

### 准确性指标
- **流量预测偏差率**: 平均偏差率控制在10%以内
- **积压时段预测**: 时段偏差不超过1小时
- **峰值积压预测**: 偏差率不超过15%

## 部署方案

### 开发环境
- Python 3.8+
- PyQt5 5.15+
- 相关依赖包（见requirements.txt）

### 生产部署
- 使用PyInstaller打包成独立exe文件
- 包含所有依赖和数据文件
- 支持Windows 10/11系统
- 无需安装Python环境

### 安装使用
1. 解压程序包到目标目录
2. 运行install.bat安装脚本
3. 或直接双击exe文件启动程序

## 项目亮点

### 技术亮点
1. **模块化设计**: 清晰的代码结构，便于维护和扩展
2. **异步处理**: 使用QThread避免界面卡顿
3. **实时推演**: 支持推演速度调整和暂停/继续控制
4. **数据可视化**: 多种图表类型，直观展示分析结果
5. **错误处理**: 完善的异常处理机制

### 功能亮点
1. **三大核心指标**: 完整实现评审要求的三个量化指标
2. **情景分析**: 支持多种干扰情景的模拟分析
3. **最优决策**: 自动生成延误最少的决策方案
4. **用户友好**: 操作简单，界面美观，易于使用

### 创新点
1. **集成化平台**: 将预测、推演、决策集成在一个系统中
2. **可视化推演**: 实时图表展示推演过程
3. **参数化配置**: 灵活的参数设置，适应不同场景
4. **一键部署**: exe打包，无需复杂安装过程

## 使用说明

### 快速开始
1. 启动程序后，首先在"数据管理"中加载航班数据
2. 在"流量预测"中设置预测参数并开始预测
3. 在"运行推演"中配置推演参数并启动推演
4. 在"结果展示"中查看详细的分析结果和图表

### 注意事项
- 确保数据文件格式正确（Excel格式）
- 推演过程中可以调整速度和暂停/继续
- 建议在推演前先进行流量预测
- 结果数据可以导出保存

## 技术支持

如有技术问题或改进建议，请联系开发团队。

---
**开发团队**: 挑战杯项目组  
**版本**: v1.0.0  
**日期**: 2025年1月  
**许可**: 仅供竞赛使用
