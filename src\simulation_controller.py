#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
推演控制器模块
实现推演过程的控制功能，包括速度调整、暂停/继续、重置等
"""

from PyQt5.QtCore import QObject, QTimer, pyqtSignal
from PyQt5.QtWidgets import QMessageBox
import time

class SimulationController(QObject):
    """推演控制器"""
    
    # 信号定义
    simulation_started = pyqtSignal()
    simulation_paused = pyqtSignal()
    simulation_resumed = pyqtSignal()
    simulation_stopped = pyqtSignal()
    simulation_reset = pyqtSignal()
    
    hour_updated = pyqtSignal(int)  # 当前推演小时
    status_updated = pyqtSignal(dict)  # 当前状态数据
    progress_updated = pyqtSignal(int)  # 进度百分比
    
    def __init__(self):
        super().__init__()
        
        # 推演状态
        self.is_running = False
        self.is_paused = False
        self.current_hour = 0
        self.simulation_speed = 1.0  # 推演速度倍数
        
        # 推演数据
        self.traffic_data = None
        self.operation_results = None
        self.disruptions = None
        
        # 定时器
        self.timer = QTimer()
        self.timer.timeout.connect(self.advance_simulation)
        
        # 推演参数
        self.capacity_per_hour = 20
        self.delay_threshold = 10
        
        # 实时状态
        self.current_backlog = 0
        self.hourly_status = {}
        self.backlog_periods = []
        self.peak_backlog = {'hour': 0, 'count': 0}
    
    def set_simulation_data(self, traffic_data, disruptions=None):
        """
        设置推演数据
        
        Args:
            traffic_data: 流量预测数据
            disruptions: 干扰事件列表
        """
        self.traffic_data = traffic_data
        self.disruptions = disruptions or []
        
    def set_simulation_speed(self, speed):
        """
        设置推演速度
        
        Args:
            speed: 速度倍数 (1-10)
        """
        self.simulation_speed = max(1, min(10, speed))
        if self.is_running and not self.is_paused:
            # 重新启动定时器以应用新速度
            self.timer.stop()
            interval = max(100, int(1000 / self.simulation_speed))  # 最小100ms间隔
            self.timer.start(interval)
    
    def set_capacity(self, capacity):
        """设置机场容量"""
        self.capacity_per_hour = capacity
    
    def start_simulation(self):
        """开始推演"""
        if not self.traffic_data:
            return False
        
        if self.is_paused:
            # 从暂停状态恢复
            self.resume_simulation()
        else:
            # 全新开始
            self.reset_simulation_state()
            self.is_running = True
            self.is_paused = False
            
            # 启动定时器
            interval = max(100, int(1000 / self.simulation_speed))
            self.timer.start(interval)
            
            self.simulation_started.emit()
        
        return True
    
    def pause_simulation(self):
        """暂停推演"""
        if self.is_running and not self.is_paused:
            self.is_paused = True
            self.timer.stop()
            self.simulation_paused.emit()
    
    def resume_simulation(self):
        """恢复推演"""
        if self.is_running and self.is_paused:
            self.is_paused = False
            interval = max(100, int(1000 / self.simulation_speed))
            self.timer.start(interval)
            self.simulation_resumed.emit()
    
    def stop_simulation(self):
        """停止推演"""
        self.is_running = False
        self.is_paused = False
        self.timer.stop()
        self.simulation_stopped.emit()
    
    def reset_simulation(self):
        """重置推演"""
        self.stop_simulation()
        self.reset_simulation_state()
        self.simulation_reset.emit()
    
    def reset_simulation_state(self):
        """重置推演状态"""
        self.current_hour = 0
        self.current_backlog = 0
        self.hourly_status = {}
        self.backlog_periods = []
        self.peak_backlog = {'hour': 0, 'count': 0}
    
    def advance_simulation(self):
        """推进推演一个时间步"""
        if self.current_hour >= 24:
            # 推演完成
            self.stop_simulation()
            return
        
        # 计算当前小时的状态
        hour_status = self.calculate_hour_status(self.current_hour)
        self.hourly_status[self.current_hour] = hour_status
        
        # 更新积压状态
        self.current_backlog = hour_status['backlog_end']
        
        # 更新峰值积压
        if self.current_backlog > self.peak_backlog['count']:
            self.peak_backlog = {'hour': self.current_hour, 'count': self.current_backlog}
        
        # 发送状态更新信号
        self.hour_updated.emit(self.current_hour)
        self.status_updated.emit(hour_status)
        
        # 计算进度
        progress = int((self.current_hour + 1) / 24 * 100)
        self.progress_updated.emit(progress)
        
        # 推进到下一小时
        self.current_hour += 1
    
    def calculate_hour_status(self, hour):
        """
        计算指定小时的运行状态
        
        Args:
            hour: 小时 (0-23)
        
        Returns:
            Dict: 小时状态数据
        """
        # 获取计划流量
        planned_flights = self.traffic_data.get(hour, {'total': 0})['total']
        
        # 检查干扰事件
        capacity = self.capacity_per_hour
        disruption_type = None
        
        for disruption in self.disruptions:
            if disruption['start_hour'] <= hour <= disruption['end_hour']:
                if disruption['type'] == 'stop_operations':
                    capacity = 0
                    disruption_type = '停止起降'
                elif disruption['type'] == 'reduced_capacity':
                    capacity = int(capacity * 0.5)
                    disruption_type = '容量减半'
                break
        
        # 计算处理能力
        total_demand = self.current_backlog + planned_flights
        processed = min(total_demand, capacity)
        new_backlog = max(0, total_demand - processed)
        
        # 判断是否拥堵
        is_congested = new_backlog > self.delay_threshold
        
        hour_status = {
            'hour': hour,
            'planned_flights': planned_flights,
            'backlog_start': self.current_backlog,
            'capacity': capacity,
            'processed': processed,
            'backlog_end': new_backlog,
            'is_congested': is_congested,
            'disruption_type': disruption_type,
            'utilization_rate': (processed / capacity * 100) if capacity > 0 else 0
        }
        
        return hour_status
    
    def get_current_results(self):
        """
        获取当前推演结果
        
        Returns:
            Dict: 推演结果数据
        """
        # 计算积压时段
        backlog_periods = []
        backlog_start = None
        
        for hour in range(self.current_hour):
            if hour in self.hourly_status:
                is_congested = self.hourly_status[hour]['is_congested']
                
                if is_congested and backlog_start is None:
                    backlog_start = hour
                elif not is_congested and backlog_start is not None:
                    backlog_periods.append({
                        'start': backlog_start,
                        'end': hour - 1,
                        'duration': hour - backlog_start
                    })
                    backlog_start = None
        
        # 处理最后的积压时段
        if backlog_start is not None:
            backlog_periods.append({
                'start': backlog_start,
                'end': self.current_hour - 1,
                'duration': self.current_hour - backlog_start
            })
        
        # 计算最晚运行时段
        last_operation_hour = 23
        if self.current_backlog > 0:
            extra_hours = int(self.current_backlog / self.capacity_per_hour) + 1
            last_operation_hour = min(23 + extra_hours, 30)
        
        return {
            'hourly_status': self.hourly_status.copy(),
            'backlog_periods': backlog_periods,
            'peak_backlog': self.peak_backlog.copy(),
            'last_operation_hour': last_operation_hour,
            'current_hour': self.current_hour,
            'is_complete': self.current_hour >= 24
        }
    
    def export_results(self):
        """
        导出推演结果
        
        Returns:
            Dict: 完整的推演结果
        """
        results = self.get_current_results()
        
        # 添加统计信息
        total_planned = sum(status.get('planned_flights', 0) 
                          for status in self.hourly_status.values())
        total_processed = sum(status.get('processed', 0) 
                            for status in self.hourly_status.values())
        
        results['statistics'] = {
            'total_planned_flights': total_planned,
            'total_processed_flights': total_processed,
            'processing_efficiency': (total_processed / total_planned * 100) if total_planned > 0 else 0,
            'total_backlog_hours': sum(period['duration'] for period in results['backlog_periods']),
            'max_hourly_backlog': self.peak_backlog['count']
        }
        
        return results
