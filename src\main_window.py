#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口模块
实现应用程序的主界面
"""

import sys
import os
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                             QTabWidget, QLabel, QPushButton, QFileDialog,
                             QTextEdit, QProgressBar, QGroupBox, QGridLayout,
                             QSpinBox, QComboBox, QDateEdit, QMessageBox,
                             QSplitter, QFrame, QListWidget, QListWidgetItem,
                             QScrollArea, QTableWidget, QTableWidgetItem, QSizePolicy, QAbstractItemView)
from PyQt5.QtCore import Qt, QDate, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QPixmap, QPalette, QColor, QIcon

try:
    from prediction_models import FlightTrafficPredictor, FlightOperationSimulator, OptimalDecisionMaker, FlightDelayPredictor
    from simulation_models import FlightSimulator
    from visualization import ChartWidget, PredictionChartWidget
except ImportError:
    from .prediction_models import FlightTrafficPredictor, FlightOperationSimulator, OptimalDecisionMaker, FlightDelayPredictor
    from .simulation_models import FlightSimulator
    from .visualization import ChartWidget, PredictionChartWidget

class PredictionWorker(QThread):
    """预测计算工作线程"""
    
    progress_updated = pyqtSignal(int)
    result_ready = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, predictor, simulator, target_date, weather_data=None, disruptions=None):
        super().__init__()
        self.predictor = predictor
        self.simulator = simulator
        self.target_date = target_date
        self.weather_data = weather_data
        self.disruptions = disruptions
    
    def run(self):
        try:
            # 流量预测
            self.progress_updated.emit(20)
            traffic_prediction = self.predictor.predict_hourly_traffic(
                self.target_date, self.weather_data
            )
            
            # 运行状态推演
            self.progress_updated.emit(50)
            operation_result = self.simulator.simulate_24h_operations(
                traffic_prediction, self.disruptions
            )
            
            # 最优决策生成
            self.progress_updated.emit(80)
            decision_maker = OptimalDecisionMaker()
            optimal_plan = decision_maker.generate_optimal_delay_plan(
                traffic_prediction, {}
            )
            
            self.progress_updated.emit(100)
            
            result = {
                'traffic_prediction': traffic_prediction,
                'operation_result': operation_result,
                'optimal_plan': optimal_plan
            }
            
            self.result_ready.emit(result)
            
        except Exception as e:
            self.error_occurred.emit(str(e))

class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.predictor = FlightTrafficPredictor()
        self.simulator = FlightOperationSimulator()
        self.current_results = None

        # 用于数据分页的变量
        self.flight_data_df = None
        self.current_page = 0
        self.rows_per_page = 50  # 每页显示50行

        # 延误预测分页变量
        self.delay_current_page = 0
        self.delay_rows_per_page = 50  # 每页显示50行，与数据管理保持一致
        self.delay_full_data = None

        # 推演器实例
        self.flight_simulator = FlightSimulator()

        self.init_ui()
        self.setup_connections()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("航班流量预测与运行状态推演系统 v1.0")
        self.setGeometry(100, 100, 1400, 900)

        # 设置窗口图标
        icon_path = os.path.join(os.path.dirname(__file__), '..', 'resources', 'app_icon.png')
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))
        
        # 设置窗口样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QTabWidget::pane {
                border: 1px solid #c0c0c0;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #e0e0e0;
                padding: 8px 16px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #0078d4;
            }
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:pressed {
                background-color: #005a9e;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建标题
        title_label = QLabel("广州白云机场航班流量预测与运行状态推演系统")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        title_label.setStyleSheet("color: #0078d4; margin: 10px;")
        main_layout.addWidget(title_label)
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # 创建各个选项卡
        self.create_data_tab()
        self.create_prediction_tab()
        self.create_delay_prediction_tab()
        self.create_simulation_tab()
        self.create_results_tab()
        
        # 创建状态栏
        self.statusBar().showMessage("就绪")
        
        # 创建进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.statusBar().addPermanentWidget(self.progress_bar)
    
    def create_data_tab(self):
        """创建数据管理选项卡"""
        data_widget = QWidget()
        layout = QVBoxLayout(data_widget)
        
        # 数据文件管理组
        file_group = QGroupBox("数据文件管理")
        file_layout = QGridLayout(file_group)
        
        # 航班数据文件
        file_layout.addWidget(QLabel("航班数据文件:"), 0, 0)
        self.flight_file_label = QLabel("未选择文件")
        self.flight_file_label.setStyleSheet("border: 1px solid #ccc; padding: 5px;")
        file_layout.addWidget(self.flight_file_label, 0, 1)
        
        self.load_flight_btn = QPushButton("选择航班数据")
        file_layout.addWidget(self.load_flight_btn, 0, 2)
        
        # 天气数据文件
        file_layout.addWidget(QLabel("天气数据文件:"), 1, 0)
        self.weather_file_label = QLabel("未选择文件")
        self.weather_file_label.setStyleSheet("border: 1px solid #ccc; padding: 5px;")
        file_layout.addWidget(self.weather_file_label, 1, 1)
        
        self.load_weather_btn = QPushButton("选择天气数据")
        file_layout.addWidget(self.load_weather_btn, 1, 2)
        
        layout.addWidget(file_group)

        # 数据预览组
        preview_group = QGroupBox("数据预览")
        preview_group.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        preview_layout = QVBoxLayout(preview_group)

        # 创建选项卡用于不同类型的预览
        self.preview_tabs = QTabWidget()
        self.preview_tabs.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # 航班数据预览
        flight_preview_widget = QWidget()
        flight_preview_layout = QVBoxLayout(flight_preview_widget)
        flight_preview_layout.setContentsMargins(0, 0, 0, 0)

        self.flight_data_table = QTableWidget()
        font = QFont()
        font.setPointSize(8)
        self.flight_data_table.setFont(font)
        self.flight_data_table.setAlternatingRowColors(True)
        self.flight_data_table.setStyleSheet('''
            QTableWidget {
                gridline-color: #dcdcdc;
                selection-background-color: #e0e0e0;
                selection-color: #333;
            }
            QHeaderView::section {
                background-color: #0078d4;
                color: white;
                padding: 4px;
                border: 1px solid #006bbd;
                font-weight: bold;
            }
        ''')
        
        pagination_widget = QWidget()
        pagination_layout = QHBoxLayout(pagination_widget)
        self.prev_page_btn = QPushButton("<< 上一页")
        self.next_page_btn = QPushButton("下一页 >>")
        self.page_label = QLabel("请先加载数据")
        self.page_label.setAlignment(Qt.AlignCenter)

        pagination_layout.addWidget(self.prev_page_btn)
        pagination_layout.addStretch()
        pagination_layout.addWidget(self.page_label)
        pagination_layout.addStretch()
        pagination_layout.addWidget(self.next_page_btn)

        flight_preview_layout.addWidget(self.flight_data_table)
        flight_preview_layout.addWidget(pagination_widget)

        self.preview_tabs.addTab(flight_preview_widget, "航班数据")

        # 天气数据预览
        weather_preview_widget = QWidget()
        weather_preview_layout = QVBoxLayout(weather_preview_widget)
        weather_preview_layout.setContentsMargins(0, 0, 0, 0)

        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setMinimumHeight(300)

        self.image_container = QWidget()
        self.image_container_layout = QHBoxLayout(self.image_container)
        self.image_container_layout.setSpacing(10)
        scroll_area.setWidget(self.image_container)

        weather_preview_layout.addWidget(scroll_area)

        self.preview_tabs.addTab(weather_preview_widget, "天气数据")

        

        preview_layout.addWidget(self.preview_tabs)
        layout.addWidget(preview_group)

        # 移除弹性空间，让预览组占满剩余空间
        
        self.tab_widget.addTab(data_widget, "数据管理")
    
    def create_prediction_tab(self):
        """创建预测配置选项卡"""
        prediction_widget = QWidget()
        layout = QVBoxLayout(prediction_widget)

        # 预测参数组
        param_group = QGroupBox("预测参数设置")
        param_layout = QGridLayout(param_group)

        # 目标日期
        param_layout.addWidget(QLabel("预测日期:"), 0, 0)
        self.target_date = QDateEdit()
        self.target_date.setDate(QDate.currentDate().addDays(1))
        self.target_date.setCalendarPopup(True)
        param_layout.addWidget(self.target_date, 0, 1)

        # 修改为固定的运行时段
        param_layout.addWidget(QLabel("运行时段:"), 1, 0)
        self.time_display_label = QLabel("7:00 - 23:00")
        # 设置样式使其看起来像输入框但不可编辑
        self.time_display_label.setStyleSheet("""
            QLabel {
                border: 1px solid #c0c0c0;
                border-radius: 3px;
                padding: 5px 8px;
                background-color: #f0f0f0;
                color: #333;
                font-size: 9pt;
                min-height: 20px;
            }
        """)
        param_layout.addWidget(self.time_display_label, 1, 1)

        layout.addWidget(param_group)

        # 开始预测按钮
        self.start_prediction_btn = QPushButton("开始流量预测")
        self.start_prediction_btn.setMinimumHeight(40)
        layout.addWidget(self.start_prediction_btn)

        # 流量预测结果展示组
        results_group = QGroupBox("流量预测结果")
        results_group.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        results_layout = QVBoxLayout(results_group)

        # 创建选项卡用于表格和图表展示
        self.prediction_tabs = QTabWidget()
        self.prediction_tabs.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # 预测数据表格
        table_widget = QWidget()
        table_layout = QVBoxLayout(table_widget)
        table_layout.setContentsMargins(0, 0, 0, 0)

        self.prediction_table = QTableWidget()
        font = QFont()
        font.setPointSize(8)
        self.prediction_table.setFont(font)
        self.prediction_table.setAlternatingRowColors(True)
        self.prediction_table.setStyleSheet('''
            QTableWidget {
                gridline-color: #dcdcdc;
                selection-background-color: #e0e0e0;
                selection-color: #333;
            }
            QHeaderView::section {
                background-color: #0078d4;
                color: white;
                padding: 4px;
                border: 1px solid #006bbd;
                font-weight: bold;
            }
        ''')

        table_layout.addWidget(self.prediction_table)
        self.prediction_tabs.addTab(table_widget, "预测数据表格")

        # 预测结果图表
        self.prediction_chart_widget = PredictionChartWidget()
        self.prediction_tabs.addTab(self.prediction_chart_widget, "预测结果图表")

        results_layout.addWidget(self.prediction_tabs)
        layout.addWidget(results_group)

        self.tab_widget.addTab(prediction_widget, "流量预测")

    def create_delay_prediction_tab(self):
        """创建延误预测选项卡"""
        delay_widget = QWidget()
        layout = QVBoxLayout(delay_widget)

        # 预测参数设置组
        param_group = QGroupBox("预测参数设置")
        param_layout = QGridLayout(param_group)

        # 预测日期
        param_layout.addWidget(QLabel("预测日期:"), 0, 0)
        self.delay_target_date = QDateEdit()
        self.delay_target_date.setDate(QDate.currentDate())
        self.delay_target_date.setCalendarPopup(True)
        param_layout.addWidget(self.delay_target_date, 0, 1)

        # 延误阈值设置（固定为15分钟）
        param_layout.addWidget(QLabel("延误阈值(分钟):"), 1, 0)
        self.delay_threshold_label = QLabel("15 分钟")
        # 设置样式使其看起来像输入框但不可编辑，与流量预测保持一致
        self.delay_threshold_label.setStyleSheet("""
            QLabel {
                border: 1px solid #c0c0c0;
                border-radius: 3px;
                padding: 5px 8px;
                background-color: #f0f0f0;
                color: #333;
                font-size: 9pt;
                min-height: 20px;
            }
        """)
        param_layout.addWidget(self.delay_threshold_label, 1, 1)

        layout.addWidget(param_group)

        # 开始预测按钮
        self.start_delay_prediction_btn = QPushButton("开始延误预测")
        self.start_delay_prediction_btn.setMinimumHeight(40)
        layout.addWidget(self.start_delay_prediction_btn)

        # 延误预测结果展示组
        results_group = QGroupBox("延误预测结果")
        results_group.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        results_layout = QVBoxLayout(results_group)

        # 创建选项卡容器
        self.delay_prediction_tabs = QTabWidget()

        # 延误预测数据表格
        table_widget = QWidget()
        table_layout = QVBoxLayout(table_widget)

        self.delay_prediction_table = QTableWidget()
        # 设置表格样式，与数据管理模块完全保持一致
        font = QFont()
        font.setPointSize(8)
        self.delay_prediction_table.setFont(font)
        self.delay_prediction_table.setAlternatingRowColors(True)
        self.delay_prediction_table.setStyleSheet('''
            QTableWidget {
                gridline-color: #dcdcdc;
                selection-background-color: #e0e0e0;
                selection-color: #333;
            }
            QHeaderView::section {
                background-color: #0078d4;
                color: white;
                padding: 4px;
                border: 1px solid #006bbd;
                font-weight: bold;
            }
        ''')

        table_layout.addWidget(self.delay_prediction_table)

        # 添加分页控件
        delay_pagination_widget = QWidget()
        delay_pagination_layout = QHBoxLayout(delay_pagination_widget)
        self.delay_prev_page_btn = QPushButton("<< 上一页")
        self.delay_page_info_label = QLabel("请先进行延误预测")
        self.delay_page_info_label.setAlignment(Qt.AlignCenter)
        self.delay_next_page_btn = QPushButton("下一页 >>")

        delay_pagination_layout.addWidget(self.delay_prev_page_btn)
        delay_pagination_layout.addStretch()
        delay_pagination_layout.addWidget(self.delay_page_info_label)
        delay_pagination_layout.addStretch()
        delay_pagination_layout.addWidget(self.delay_next_page_btn)

        table_layout.addWidget(delay_pagination_widget)
        self.delay_prediction_tabs.addTab(table_widget, "延误预测数据表格")

        # 延误预测结果图表
        from .visualization import DelayChartWidget
        self.delay_chart_widget = DelayChartWidget()
        self.delay_prediction_tabs.addTab(self.delay_chart_widget, "延误预测图表")

        results_layout.addWidget(self.delay_prediction_tabs)
        layout.addWidget(results_group)

        self.tab_widget.addTab(delay_widget, "延误预测")

    def create_simulation_tab(self):
        """创建运行推演选项卡"""
        simulation_widget = QWidget()
        layout = QVBoxLayout(simulation_widget)

        # 推演参数设置组
        param_group = QGroupBox("推演参数设置")
        param_layout = QGridLayout(param_group)

        # 预留参数设置位置
        param_layout.addWidget(QLabel("参数设置区域（预留）"), 0, 0)
        param_placeholder = QLabel("暂无参数需要设置")
        param_placeholder.setStyleSheet("""
            QLabel {
                border: 1px solid #c0c0c0;
                border-radius: 3px;
                padding: 20px;
                background-color: #f8f9fa;
                color: #666;
                font-size: 9pt;
                text-align: center;
            }
        """)
        param_layout.addWidget(param_placeholder, 0, 1)

        layout.addWidget(param_group)

        # 开始推演按钮
        self.start_simulation_btn = QPushButton("开始推演")
        self.start_simulation_btn.setMinimumHeight(40)
        self.start_simulation_btn.setEnabled(False)  # 初始禁用，需要先进行延误预测
        self.start_simulation_btn.clicked.connect(self.start_flight_simulation)
        layout.addWidget(self.start_simulation_btn)

        # 推演结果展示组
        results_group = QGroupBox("推演结果")
        results_group.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        results_layout = QVBoxLayout(results_group)

        # 推演结果表格
        self.simulation_table = QTableWidget()
        # 设置表格样式，与延误预测表格保持一致
        font = QFont()
        font.setPointSize(8)
        self.simulation_table.setFont(font)
        self.simulation_table.setAlternatingRowColors(True)
        self.simulation_table.setStyleSheet('''
            QTableWidget {
                gridline-color: #dcdcdc;
                selection-background-color: #e0e0e0;
                selection-color: #333;
            }
            QHeaderView::section {
                background-color: #0078d4;
                color: white;
                padding: 4px;
                border: 1px solid #006bbd;
                font-weight: bold;
            }
        ''')

        results_layout.addWidget(self.simulation_table)

        layout.addWidget(results_group)

        self.tab_widget.addTab(simulation_widget, "运行推演")

    def create_results_tab(self):
        """创建结果展示选项卡"""
        results_widget = QWidget()
        layout = QVBoxLayout(results_widget)

        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)

        # 左侧：图表展示
        chart_frame = QFrame()
        chart_frame.setFrameStyle(QFrame.StyledPanel)
        chart_layout = QVBoxLayout(chart_frame)

        chart_layout.addWidget(QLabel("数据可视化"))
        self.chart_widget = ChartWidget()
        chart_layout.addWidget(self.chart_widget)

        splitter.addWidget(chart_frame)

        # 右侧：结果文本
        text_frame = QFrame()
        text_frame.setFrameStyle(QFrame.StyledPanel)
        text_layout = QVBoxLayout(text_frame)

        text_layout.addWidget(QLabel("分析结果"))
        self.results_text = QTextEdit()
        self.results_text.setReadOnly(True)
        text_layout.addWidget(self.results_text)

        splitter.addWidget(text_frame)

        # 设置分割器比例
        splitter.setSizes([800, 400])

        layout.addWidget(splitter)

        self.tab_widget.addTab(results_widget, "结果展示")

    def setup_connections(self):
        """设置信号连接"""
        self.load_flight_btn.clicked.connect(self.load_flight_data)
        self.load_weather_btn.clicked.connect(self.load_weather_data)
        self.start_prediction_btn.clicked.connect(self.start_prediction)
        self.start_delay_prediction_btn.clicked.connect(self.start_delay_prediction)

        # 分页按钮连接
        self.prev_page_btn.clicked.connect(self.prev_page)
        self.next_page_btn.clicked.connect(self.next_page)

        # 延误预测分页按钮连接
        self.delay_prev_page_btn.clicked.connect(self.delay_prev_page)
        self.delay_next_page_btn.clicked.connect(self.delay_next_page)





    def load_flight_data(self):
        """加载航班数据并进行分页显示"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择航班数据文件", "", "Excel files (*.xlsx *.xls)"
        )

        if file_path:
            # 显示进度条
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            self.statusBar().showMessage("正在加载航班数据...")

            try:
                import pandas as pd

                # 更新进度
                self.progress_bar.setValue(30)
                self.statusBar().showMessage("正在读取Excel文件...")

                self.flight_data_df = pd.read_excel(file_path)

                # 更新进度
                self.progress_bar.setValue(70)
                self.statusBar().showMessage("正在处理数据...")

                self.predictor.load_data(file_path)  # 预测器仍然需要完整数据
                self.flight_file_label.setText(os.path.basename(file_path))

                self.current_page = 0

                # 更新进度
                self.progress_bar.setValue(90)
                self.statusBar().showMessage("正在生成预览...")

                self.display_current_page()
                self.preview_tabs.setCurrentIndex(0)

                # 完成
                self.progress_bar.setValue(100)
                self.statusBar().showMessage("航班数据加载成功")

                # 延迟隐藏进度条
                QTimer.singleShot(1000, lambda: self.progress_bar.setVisible(False))

            except Exception as e:
                self.progress_bar.setVisible(False)
                QMessageBox.critical(self, "错误", f"加载航班数据失败: {str(e)}")

    def display_current_page(self):
        """显示当前页的数据"""
        if self.flight_data_df is None:
            return

        total_rows = len(self.flight_data_df)
        total_pages = (total_rows + self.rows_per_page - 1) // self.rows_per_page

        start_row = self.current_page * self.rows_per_page
        end_row = min(start_row + self.rows_per_page, total_rows)

        page_df = self.flight_data_df.iloc[start_row:end_row]

        self.flight_data_table.setRowCount(len(page_df))
        self.flight_data_table.setColumnCount(len(page_df.columns))
        self.flight_data_table.setHorizontalHeaderLabels(page_df.columns)

        for row in range(len(page_df)):
            for col in range(len(page_df.columns)):
                item = QTableWidgetItem(str(page_df.iat[row, col]))
                self.flight_data_table.setItem(row, col, item)

        self.flight_data_table.resizeColumnsToContents()
        self.page_label.setText(f"第 {self.current_page + 1} / {total_pages} 页")

        # 更新按钮状态
        self.prev_page_btn.setEnabled(self.current_page > 0)
        self.next_page_btn.setEnabled(self.current_page < total_pages - 1)

    def prev_page(self):
        """切换到上一页"""
        if self.current_page > 0:
            self.current_page -= 1
            self.display_current_page()

    def next_page(self):
        """切换到下一页"""
        if self.flight_data_df is not None:
            total_rows = len(self.flight_data_df)
            total_pages = (total_rows + self.rows_per_page - 1) // self.rows_per_page
            if self.current_page < total_pages - 1:
                self.current_page += 1
                self.display_current_page()

    def load_weather_data(self):
        """加载天气数据（支持文件夹和子文件夹结构）"""
        # 询问用户选择加载方式
        reply = QMessageBox.question(
            self, "选择加载方式",
            "请选择天气数据加载方式：\n\n"
            "是(Yes) - 选择文件夹（支持子文件夹扫描）\n"
            "否(No) - 选择单个图片文件",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes
        )

        if reply == QMessageBox.Yes:
            # 选择文件夹
            folder_path = QFileDialog.getExistingDirectory(
                self, "选择天气数据文件夹", ""
            )

            if folder_path:
                self.load_weather_from_folder(folder_path)
        else:
            # 选择单个文件
            file_paths, _ = QFileDialog.getOpenFileNames(
                self, "选择天气数据文件", "",
                "图片文件 (*.png *.jpg *.jpeg *.bmp)"
            )

            if file_paths:
                self.load_weather_from_files(file_paths)

    def load_weather_from_folder(self, folder_path):
        """从文件夹加载天气数据（扫描子文件夹）"""
        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.statusBar().showMessage("正在扫描文件夹...")

        try:
            # 支持的图片格式
            image_extensions = {'.png', '.jpg', '.jpeg', '.bmp', '.gif', '.tiff'}
            image_files = []

            # 更新进度
            self.progress_bar.setValue(20)
            self.statusBar().showMessage("正在扫描子文件夹...")

            # 递归扫描文件夹和子文件夹
            for root, dirs, files in os.walk(folder_path):
                for file in files:
                    file_ext = os.path.splitext(file)[1].lower()
                    if file_ext in image_extensions:
                        full_path = os.path.join(root, file)
                        image_files.append(full_path)

            if not image_files:
                QMessageBox.information(self, "提示", "在选择的文件夹中未找到图片文件")
                self.progress_bar.setVisible(False)
                return

            # 更新进度
            self.progress_bar.setValue(50)
            self.statusBar().showMessage(f"找到 {len(image_files)} 个图片文件，正在加载...")

            # 更新标签显示
            folder_name = os.path.basename(folder_path)
            self.weather_file_label.setText(f"文件夹: {folder_name} (共 {len(image_files)} 个图片)")

            # 更新进度
            self.progress_bar.setValue(70)
            self.statusBar().showMessage("正在生成图片预览...")

            # 设置基础文件夹路径，用于显示相对路径
            self._current_base_folder = folder_path
            self.update_weather_preview(image_files)

            # 完成
            self.progress_bar.setValue(100)
            self.statusBar().showMessage(f"天气数据加载成功，共 {len(image_files)} 个文件")

            # 延迟隐藏进度条
            QTimer.singleShot(1000, lambda: self.progress_bar.setVisible(False))

        except Exception as e:
            self.progress_bar.setVisible(False)
            QMessageBox.critical(self, "错误", f"加载天气数据失败: {str(e)}")

    def load_weather_from_files(self, file_paths):
        """从选择的文件加载天气数据"""
        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.statusBar().showMessage("正在加载天气数据...")

        try:
            weather_files = [os.path.basename(p) for p in file_paths]

            # 更新进度
            self.progress_bar.setValue(30)
            self.statusBar().showMessage("正在处理文件列表...")

            if len(weather_files) == 1:
                self.weather_file_label.setText(weather_files[0])
            else:
                self.weather_file_label.setText(f"已选择 {len(weather_files)} 个文件")

            # 更新进度
            self.progress_bar.setValue(60)
            self.statusBar().showMessage("正在生成图片预览...")

            # 清除基础文件夹路径（单文件模式）
            self._current_base_folder = None
            self.update_weather_preview(file_paths)

            # 完成
            self.progress_bar.setValue(100)
            self.statusBar().showMessage(f"天气数据加载成功，共 {len(file_paths)} 个文件")

            # 延迟隐藏进度条
            QTimer.singleShot(1000, lambda: self.progress_bar.setVisible(False))

        except Exception as e:
            self.progress_bar.setVisible(False)
            QMessageBox.critical(self, "错误", f"加载天气数据失败: {str(e)}")

    

    def update_weather_preview(self, image_files):
        """更新天气图片预览"""
        self.clear_image_container()

        for image_file in image_files:
            self.add_image_to_container(image_file)
        
        self.preview_tabs.setCurrentIndex(1) # 确保切换到天气预览

    

    def clear_image_container(self):
        """清空图片容器"""
        while self.image_container_layout.count():
            child = self.image_container_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

    def add_image_to_container(self, image_path):
        """添加图片到横向容器"""
        try:
            # 创建图片标签
            image_label = QLabel()
            image_label.setAlignment(Qt.AlignCenter)
            image_label.setStyleSheet("border: 1px solid #ccc; margin: 5px;")

            # 加载并缩放图片
            pixmap = QPixmap(image_path)
            if not pixmap.isNull():
                # 设置最小大小，保持宽高比，允许缩放
                scaled_pixmap = pixmap.scaled(448, 448, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                image_label.setPixmap(scaled_pixmap)
                image_label.setMinimumSize(260, 210)
                image_label.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Preferred)
            else:
                image_label.setText("无法加载图片")
                image_label.setMinimumSize(260, 210)
                image_label.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Preferred)

            # 添加文件信息标签
            container = QWidget()
            container_layout = QVBoxLayout(container)
            container_layout.setSpacing(2)

            # 显示文件名
            filename_label = QLabel(os.path.basename(image_path))
            filename_label.setAlignment(Qt.AlignCenter)
            filename_label.setStyleSheet("font-size: 12px; font-weight: bold; color: #000000;")

            # 如果是从文件夹加载的，显示相对路径
            if hasattr(self, '_current_base_folder') and self._current_base_folder:
                try:
                    rel_path = os.path.relpath(image_path, self._current_base_folder)
                    folder_path = os.path.dirname(rel_path)
                    if folder_path and folder_path != '.':
                        path_label = QLabel(f"📁 {folder_path}")
                        path_label.setAlignment(Qt.AlignCenter)
                        path_label.setStyleSheet("font-size: 10px; color: #666666;")
                        container_layout.addWidget(path_label)
                except:
                    pass  # 如果路径处理失败，忽略

            container_layout.addWidget(image_label)
            container_layout.addWidget(filename_label)

            self.image_container_layout.addWidget(container)

        except Exception as e:
            error_label = QLabel(f"加载失败:\n{os.path.basename(image_path)}")
            error_label.setAlignment(Qt.AlignCenter)
            error_label.setMinimumSize(260, 210)
            error_label.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Preferred)
            error_label.setStyleSheet("border: 1px solid #ccc; margin: 5px; color: red;")
            self.image_container_layout.addWidget(error_label)

    def start_prediction(self):
        """开始流量预测"""
        # 检查是否已加载航班数据
        if self.flight_data_df is None:
            QMessageBox.warning(self, "警告", "请先加载航班数据文件")
            return

        try:
            # 显示进度条
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(20)
            self.statusBar().showMessage("正在进行流量预测...")

            # 获取参数
            target_date = self.target_date.date().toString("yyyy-MM-dd")

            # 直接调用预测方法
            prediction_result = self.predictor.predict_hourly_traffic(target_date)

            self.progress_bar.setValue(80)

            # 更新预测结果显示
            self.update_prediction_display(prediction_result)

            self.progress_bar.setValue(100)
            self.statusBar().showMessage("流量预测完成")

            # 隐藏进度条
            self.progress_bar.setVisible(False)

        except Exception as e:
            self.progress_bar.setVisible(False)
            QMessageBox.critical(self, "预测错误", f"预测过程中发生错误：{str(e)}")
            self.statusBar().showMessage("预测失败")

    def update_prediction_display(self, prediction_df):
        """更新流量预测结果显示"""
        # 更新表格显示
        self.update_prediction_table(prediction_df)

        # 更新图表显示
        self.prediction_chart_widget.update_prediction_chart(prediction_df)

        # 同时更新结果展示模块的流量预测图表
        self.chart_widget.update_traffic_chart(prediction_df)

    def update_prediction_table(self, prediction_df):
        """更新预测数据表格"""
        if prediction_df is None or prediction_df.empty:
            self.prediction_table.setRowCount(0)
            self.prediction_table.setColumnCount(0)
            return

        # 设置表格行列数
        self.prediction_table.setRowCount(len(prediction_df))
        self.prediction_table.setColumnCount(len(prediction_df.columns))

        # 设置表头
        self.prediction_table.setHorizontalHeaderLabels(prediction_df.columns.tolist())

        # 填充数据
        for row in range(len(prediction_df)):
            for col in range(len(prediction_df.columns)):
                value = prediction_df.iloc[row, col]
                # 格式化数值显示
                if isinstance(value, float):
                    if col == len(prediction_df.columns) - 2:  # 偏差率列
                        item = QTableWidgetItem(f"{value:.1%}")
                    else:
                        item = QTableWidgetItem(f"{value:.3f}")
                else:
                    item = QTableWidgetItem(str(value))

                # 设置偏差>15%的行为红色
                if col == len(prediction_df.columns) - 1 and str(value) == "偏差>15%":
                    item.setBackground(QColor(255, 200, 200))

                self.prediction_table.setItem(row, col, item)

        # 调整列宽
        self.prediction_table.resizeColumnsToContents()





    def on_prediction_complete(self, results):
        """预测完成回调"""
        self.current_results = results
        self.progress_bar.setVisible(False)

        # 更新图表
        self.chart_widget.update_charts(results)

        # 更新结果文本
        self.update_results_text(results)

        # 切换到结果选项卡
        self.tab_widget.setCurrentIndex(3)

        self.statusBar().showMessage("预测计算完成")

    def on_prediction_error(self, error_msg):
        """预测错误回调"""
        self.progress_bar.setVisible(False)
        QMessageBox.critical(self, "预测错误", f"预测计算失败: {error_msg}")
        self.statusBar().showMessage("预测计算失败")

    def update_results_text(self, results):
        """更新结果文本"""
        text = "=== 预测分析结果 ===\n\n"

        # 流量预测结果
        traffic = results['traffic_prediction']
        text += "1. 流量预测结果:\n"
        for hour in range(7, 24):  # 繁忙时段
            if hour in traffic:
                data = traffic[hour]
                text += f"   {hour:02d}:00 - 进港:{data['inbound']} 出港:{data['outbound']} 总计:{data['total']}\n"

        # 运行状态推演结果
        operation = results['operation_result']
        text += "\n2. 运行状态推演结果:\n"
        text += f"   积压时段数量: {len(operation['backlog_periods'])}\n"
        if operation['backlog_periods']:
            for i, period in enumerate(operation['backlog_periods']):
                text += f"   积压时段{i+1}: {period['start']}:00 - {period['end']}:00 (持续{period['duration']}小时)\n"

        text += f"   峰值积压: {operation['peak_backlog']['count']}架次 (发生在{operation['peak_backlog']['hour']}:00)\n"
        text += f"   最晚运行时段: {operation['last_operation_hour']}:00\n"

        # 最优决策方案
        optimal = results['optimal_plan']
        text += "\n3. 最优决策方案:\n"
        text += f"   总延误时间: {optimal['total_delay_minutes']}分钟\n"
        text += f"   受影响航班数: {len(optimal['affected_flights'])}\n"

        self.results_text.setText(text)



    def start_delay_prediction(self):
        """开始延误预测"""
        # 检查是否已加载航班数据
        if self.flight_data_df is None:
            QMessageBox.warning(self, "警告", "请先加载航班数据文件")
            return

        try:
            # 获取参数
            target_date = self.delay_target_date.date().toString("yyyy-MM-dd")

            # 验证日期是否在航班数据范围内
            if not self.validate_prediction_date(target_date):
                QMessageBox.warning(self, "日期验证失败",
                                  f"选择的预测日期 {target_date} 不在已加载的航班数据范围内。\n"
                                  f"请选择航班数据覆盖的日期范围内的日期。")
                return

            # 显示进度条
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(20)
            self.statusBar().showMessage("正在进行延误预测...")

            # 使用延误预测模型进行预测
            from prediction_models import FlightDelayPredictor
            delay_predictor = FlightDelayPredictor()

            # 创建临时文件保存当前航班数据
            import tempfile
            import os
            with tempfile.NamedTemporaryFile(mode='w', suffix='.xlsx', delete=False) as temp_file:
                temp_path = temp_file.name

            # 保存当前数据到临时文件
            self.flight_data_df.to_excel(temp_path, index=False)

            try:
                # 加载数据并进行预测
                delay_predictor.load_data(temp_path)
                delay_result = delay_predictor.predict_flight_delays(target_date)

                # 更新延误预测结果显示
                self.update_delay_prediction_display(delay_result)

                self.progress_bar.setValue(100)
                self.statusBar().showMessage("延误预测完成")

            finally:
                # 清理临时文件
                if os.path.exists(temp_path):
                    os.unlink(temp_path)

            # 隐藏进度条
            self.progress_bar.setVisible(False)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"延误预测失败: {str(e)}")
            self.progress_bar.setVisible(False)
            self.statusBar().showMessage("延误预测失败")

    def validate_prediction_date(self, target_date):
        """验证预测日期是否在航班数据范围内"""
        if self.flight_data_df is None or self.flight_data_df.empty:
            return False

        try:
            import pandas as pd

            # 假设航班数据中有日期列，尝试不同的可能列名
            date_columns = ['日期', 'date', '计划起飞时间', '起飞时间', 'departure_time', 'scheduled_time']
            date_column = None

            for col in date_columns:
                if col in self.flight_data_df.columns:
                    date_column = col
                    break

            if date_column is None:
                # 如果没有找到日期列，假设数据是当前日期的
                return True

            # 提取日期部分
            flight_dates = pd.to_datetime(self.flight_data_df[date_column]).dt.date
            target_date_obj = pd.to_datetime(target_date).date()

            # 检查目标日期是否在航班数据的日期范围内
            min_date = flight_dates.min()
            max_date = flight_dates.max()

            return min_date <= target_date_obj <= max_date

        except Exception:
            # 如果验证过程出错，默认允许预测
            return True





    def update_delay_prediction_display(self, delay_df):
        """更新延误预测结果显示"""
        # 保存完整数据
        self.delay_full_data = delay_df
        self.delay_current_page = 0

        # 更新表格显示（分页）
        self.display_delay_current_page()

        # 更新图表显示
        self.delay_chart_widget.update_delay_chart(delay_df)

        # 启用推演按钮
        self.start_simulation_btn.setEnabled(True)

    def update_delay_prediction_table(self, delay_df):
        """更新延误预测数据表格"""
        if delay_df is None or delay_df.empty:
            self.delay_prediction_table.setRowCount(0)
            return

        # 设置表格行数和列数
        self.delay_prediction_table.setRowCount(len(delay_df))
        self.delay_prediction_table.setColumnCount(len(delay_df.columns))
        self.delay_prediction_table.setHorizontalHeaderLabels(delay_df.columns.tolist())

        # 填充数据
        for table_row, (_, row) in enumerate(delay_df.iterrows()):
            for j, value in enumerate(row):
                # 格式化显示文本
                if isinstance(value, (int, float)) and '延误' in delay_df.columns[j] and '分钟' in delay_df.columns[j]:
                    # 延误时间显示
                    if value == 0:
                        item_text = "0"
                    else:
                        item_text = str(value)
                else:
                    item_text = str(value)

                item = QTableWidgetItem(item_text)

                # 根据延误时间设置颜色
                if '延误' in delay_df.columns[j] and '分钟' in delay_df.columns[j] and isinstance(value, (int, float)):
                    if value >= 60:  # 延误超过60分钟
                        item.setBackground(QColor(255, 200, 200))  # 浅红色
                    elif value >= 15:  # 延误15-60分钟
                        item.setBackground(QColor(255, 255, 200))  # 浅黄色
                    elif value < 0:  # 提前（负延误）
                        item.setBackground(QColor(200, 255, 255))  # 浅青色
                    else:  # 正常（0-15分钟延误）
                        item.setBackground(QColor(200, 255, 200))  # 浅绿色

                self.delay_prediction_table.setItem(table_row, j, item)

        # 调整列宽
        self.delay_prediction_table.resizeColumnsToContents()

    def display_delay_current_page(self):
        """显示延误预测当前页数据"""
        if self.delay_full_data is None or self.delay_full_data.empty:
            self.delay_prediction_table.setRowCount(0)
            self.delay_page_info_label.setText("第 0 / 0 页")
            self.delay_prev_page_btn.setEnabled(False)
            self.delay_next_page_btn.setEnabled(False)
            return

        total_rows = len(self.delay_full_data)
        total_pages = (total_rows + self.delay_rows_per_page - 1) // self.delay_rows_per_page

        start_row = self.delay_current_page * self.delay_rows_per_page
        end_row = min(start_row + self.delay_rows_per_page, total_rows)

        page_df = self.delay_full_data.iloc[start_row:end_row]

        # 更新表格
        self.update_delay_prediction_table(page_df)

        # 更新分页信息
        self.delay_page_info_label.setText(f"第 {self.delay_current_page + 1} / {total_pages} 页")
        self.delay_prev_page_btn.setEnabled(self.delay_current_page > 0)
        self.delay_next_page_btn.setEnabled(self.delay_current_page < total_pages - 1)

    def delay_prev_page(self):
        """延误预测切换到上一页"""
        if self.delay_current_page > 0:
            self.delay_current_page -= 1
            self.display_delay_current_page()

    def delay_next_page(self):
        """延误预测切换到下一页"""
        if self.delay_full_data is not None:
            total_rows = len(self.delay_full_data)
            total_pages = (total_rows + self.delay_rows_per_page - 1) // self.delay_rows_per_page
            if self.delay_current_page < total_pages - 1:
                self.delay_current_page += 1
                self.display_delay_current_page()

    def start_flight_simulation(self):
        """开始航班积压量推演"""
        if self.delay_full_data is None or self.delay_full_data.empty:
            QMessageBox.warning(self, "警告", "请先进行延误预测")
            return

        try:
            # 显示进度条
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            self.statusBar().showMessage("正在进行推演分析...")

            # 加载延误预测数据到推演器
            self.flight_simulator.load_delay_prediction_data(self.delay_full_data)
            self.progress_bar.setValue(30)

            # 执行推演分析
            simulation_results = self.flight_simulator.simulate_flight_congestion()
            self.progress_bar.setValue(70)

            # 更新推演结果显示
            self.update_simulation_display(simulation_results)
            self.progress_bar.setValue(100)

            # 切换到结果展示选项卡的积压分析子选项卡
            self.tab_widget.setCurrentIndex(4)  # 结果展示是第5个选项卡
            self.chart_widget.tab_widget.setCurrentIndex(1)  # 积压分析是第2个子选项卡

            self.statusBar().showMessage("推演分析完成")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"推演分析失败: {str(e)}")
            self.statusBar().showMessage("推演分析失败")
        finally:
            # 隐藏进度条
            QTimer.singleShot(1000, lambda: self.progress_bar.setVisible(False))

    def update_simulation_display(self, simulation_df):
        """更新推演结果显示"""
        # 更新推演结果表格
        self.simulation_table.setRowCount(len(simulation_df))
        self.simulation_table.setColumnCount(len(simulation_df.columns))
        self.simulation_table.setHorizontalHeaderLabels(simulation_df.columns.tolist())

        # 填充数据
        for i, row in simulation_df.iterrows():
            for j, value in enumerate(row):
                item = QTableWidgetItem(str(value))

                # 根据数值设置颜色
                if j > 0:  # 非时段列
                    if isinstance(value, (int, float)) and value > 0:
                        if j == 2:  # 实际积压量列
                            item.setBackground(QColor(255, 200, 200))  # 浅红色
                        elif j == 3:  # 预测积压量列
                            item.setBackground(QColor(255, 255, 200))  # 浅黄色
                        else:  # 计划离岗数列
                            item.setBackground(QColor(200, 255, 200))  # 浅绿色

                self.simulation_table.setItem(i, j, item)

        # 调整列宽
        self.simulation_table.resizeColumnsToContents()

        # 更新数据可视化中的积压分析
        self.chart_widget.update_backlog_chart(simulation_df)


