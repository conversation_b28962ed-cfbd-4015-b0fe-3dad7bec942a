#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据可视化模块
实现各种图表的绘制和展示
"""

import matplotlib
# 设置matplotlib后端
try:
    matplotlib.use('Qt5Agg')
except:
    try:
        matplotlib.use('Agg')  # 无图形界面后端
    except:
        pass

import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import numpy as np
import pandas as pd
import seaborn as sns
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QTabWidget
from PyQt5.QtCore import Qt

# 设置matplotlib中文字体
try:
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei']
    plt.rcParams['axes.unicode_minus'] = False
except:
    pass  # 如果字体设置失败，使用默认字体

class ChartWidget(QWidget):
    """图表展示组件"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # 创建各个图表选项卡
        self.create_traffic_chart_tab()
        self.create_backlog_chart_tab()
        self.create_delay_chart_tab()
        self.create_comparison_chart_tab()
    
    def create_traffic_chart_tab(self):
        """创建流量预测图表选项卡"""
        self.traffic_widget = QWidget()
        layout = QVBoxLayout(self.traffic_widget)
        
        # 创建matplotlib图形
        self.traffic_figure = Figure(figsize=(10, 6))
        self.traffic_canvas = FigureCanvas(self.traffic_figure)
        layout.addWidget(self.traffic_canvas)
        
        self.tab_widget.addTab(self.traffic_widget, "流量预测")
    
    def create_backlog_chart_tab(self):
        """创建积压情况图表选项卡 - 显示推演分析指标"""
        self.backlog_widget = QWidget()
        layout = QVBoxLayout(self.backlog_widget)

        self.backlog_figure = Figure(figsize=(12, 8))
        self.backlog_canvas = FigureCanvas(self.backlog_figure)
        layout.addWidget(self.backlog_canvas)

        self.tab_widget.addTab(self.backlog_widget, "积压分析")
    
    def create_delay_chart_tab(self):
        """创建延误分析图表选项卡"""
        self.delay_widget = QWidget()
        layout = QVBoxLayout(self.delay_widget)
        
        self.delay_figure = Figure(figsize=(10, 6))
        self.delay_canvas = FigureCanvas(self.delay_figure)
        layout.addWidget(self.delay_canvas)
        
        self.tab_widget.addTab(self.delay_widget, "延误分析")
    
    def create_comparison_chart_tab(self):
        """创建对比分析图表选项卡"""
        self.comparison_widget = QWidget()
        layout = QVBoxLayout(self.comparison_widget)
        
        self.comparison_figure = Figure(figsize=(10, 6))
        self.comparison_canvas = FigureCanvas(self.comparison_figure)
        layout.addWidget(self.comparison_canvas)
        
        self.tab_widget.addTab(self.comparison_widget, "对比分析")
    
    def update_charts(self, results):
        """更新所有图表"""
        self.update_traffic_chart(results['traffic_prediction'])
        self.update_backlog_chart(results['operation_result'])
        self.update_delay_chart(results['optimal_plan'])
        self.update_comparison_chart(results)
    
    def update_traffic_chart(self, traffic_data):
        """更新流量预测图表 - 宏观指标展示"""
        self.traffic_figure.clear()

        # 检查数据类型，如果是DataFrame则提取宏观指标
        if hasattr(traffic_data, 'columns') and '小时偏差率' in traffic_data.columns:
            # 这是来自流量预测的DataFrame数据
            self._update_macro_indicators(traffic_data)
        else:
            # 这是原始的字典格式数据，保持原有逻辑
            self._update_traditional_chart(traffic_data)

        self.traffic_figure.tight_layout()
        self.traffic_canvas.draw()

    def _update_macro_indicators(self, prediction_df):
        """更新宏观指标展示 - 按照用户草图设计"""
        # 计算关键指标
        avg_deviation = prediction_df['小时偏差率'].mean() * 100
        max_deviation = prediction_df['小时偏差率'].max() * 100
        over_threshold_count = len(prediction_df[prediction_df['小时偏差率'] > 0.15])
        peak_hour_idx = prediction_df['实际总流量'].idxmax()
        peak_hour = prediction_df.loc[peak_hour_idx, '时段'].split('-')[0]
        peak_value = prediction_df.loc[peak_hour_idx, '实际总流量']
        max_deviation_idx = prediction_df['小时偏差率'].idxmax()
        max_deviation_hour = prediction_df.loc[max_deviation_idx, '时段'].split('-')[0]

        # 创建主图用于指标展示
        ax = self.traffic_figure.add_subplot(1, 1, 1)
        ax.set_xlim(0, 10)
        ax.set_ylim(0, 10)
        ax.axis('off')

        # 设置背景色
        ax.set_facecolor('#f8f9fa')

        # 标题
        ax.text(5, 9.3, '流量预测宏观指标', fontsize=18, fontweight='bold',
                ha='center', va='center', color='#2c3e50')

        # 定义卡片样式函数
        def draw_indicator_card(x, y, width, height, title, icon, value, status,
                              bg_color='#ffffff', border_color='#e0e0e0', text_color='#333333'):
            from matplotlib.patches import FancyBboxPatch

            # 绘制卡片背景
            card = FancyBboxPatch((x, y), width, height,
                                boxstyle="round,pad=0.1",
                                facecolor=bg_color,
                                edgecolor=border_color,
                                linewidth=2,
                                alpha=0.9)
            ax.add_patch(card)

            # 标题和图标
            ax.text(x + width/2, y + height - 0.3, f'{icon} {title}',
                   fontsize=12, fontweight='bold', ha='center', va='center', color='#555555')

            # 主要数值
            ax.text(x + width/2, y + height/2 , value,
                   fontsize=20, fontweight='bold', ha='center', va='center', color=text_color)

            # 状态标识
            ax.text(x + width/2, y + 0.3, f'({status})',
                   fontsize=10, ha='center', va='center', color=text_color)

        # 指标1：平均偏差率 (左上)
        deviation_bg = '#fff0f0' if avg_deviation >= 10 else '#f0fff0'
        deviation_border = '#ff6b6b' if avg_deviation >= 10 else '#51cf66'
        deviation_text = '#e03131' if avg_deviation >= 10 else '#2b8a3e'
        deviation_status = '不达标' if avg_deviation >= 10 else '达标'

        draw_indicator_card(1, 6.5, 3.5, 2, '平均偏差率', '%', f'{avg_deviation:.1f}%',
                          deviation_status, deviation_bg, deviation_border, deviation_text)

        # 指标2：超标时段数 (右上)
        threshold_bg = '#fff0f0' if over_threshold_count > 2 else '#f0fff0'
        threshold_border = '#ff6b6b' if over_threshold_count > 2 else '#51cf66'
        threshold_text = '#e03131' if over_threshold_count > 2 else '#2b8a3e'
        threshold_status = '不达标' if over_threshold_count > 2 else '达标'

        draw_indicator_card(5.5, 6.5, 3.5, 2, '超标时段数', '!', f'{over_threshold_count}个',
                          threshold_status, threshold_bg, threshold_border, threshold_text)

        # 指标3：最大偏差时段 (左下)
        max_dev_bg = '#fff8f0' if max_deviation >= 15 else '#ffffff'
        max_dev_border = '#ff8c42' if max_deviation >= 15 else '#e0e0e0'
        max_dev_text = '#d63031' if max_deviation >= 25 else '#e17055' if max_deviation >= 15 else '#636e72'

        draw_indicator_card(1, 4, 3.5, 2, '最大偏差时段', '※', f'{max_deviation_hour}:00',
                          f'偏差: {max_deviation:.1f}%', max_dev_bg, max_dev_border, max_dev_text)

        # 指标4：流量高峰 (右下)
        draw_indicator_card(5.5, 4, 3.5, 2, '流量高峰', '↗', f'{peak_hour}:00',
                          f'{peak_value}架次/小时', '#ffffff', '#e0e0e0', '#0984e3')

        # 总体评估区域
        overall_status = '优秀' if avg_deviation < 8 and over_threshold_count == 0 else \
                        '良好' if avg_deviation < 10 and over_threshold_count <= 1 else \
                        '一般' if avg_deviation < 15 and over_threshold_count <= 2 else '需改进'

        overall_colors = {
            '优秀': ('#e8f5e8', '#51cf66', '#2b8a3e'),
            '良好': ('#e3f2fd', '#42a5f5', '#1976d2'),
            '一般': ('#fff8e1', '#ffb74d', '#f57c00'),
            '需改进': ('#ffebee', '#ef5350', '#d32f2f')
        }

        bg_color, border_color, text_color = overall_colors[overall_status]

        # 总体评估卡片
        from matplotlib.patches import FancyBboxPatch
        eval_card = FancyBboxPatch((1, 1.5), 8, 1.8,
                                 boxstyle="round,pad=0.15",
                                 facecolor=bg_color,
                                 edgecolor=border_color,
                                 linewidth=3,
                                 alpha=0.9)
        ax.add_patch(eval_card)

        ax.text(5, 2.8, '总体评估', fontsize=14, fontweight='bold',
                ha='center', va='center', color='#555555')
        ax.text(5, 2.0, overall_status, fontsize=24, fontweight='bold',
                ha='center', va='center', color=text_color)

        # 指标说明
        ax.text(5, 0.8, '指标要求：平均偏差率<10%，超标时段数≤2个',
                fontsize=10, ha='center', style='italic', color='#868e96')

    def _update_traditional_chart(self, traffic_data):
        """更新传统图表展示（保持原有逻辑）"""
        # 创建子图
        ax1 = self.traffic_figure.add_subplot(2, 1, 1)
        ax2 = self.traffic_figure.add_subplot(2, 1, 2)

        # 准备数据
        hours = list(range(24))
        inbound_data = [traffic_data.get(h, {'inbound': 0})['inbound'] for h in hours]
        outbound_data = [traffic_data.get(h, {'outbound': 0})['outbound'] for h in hours]
        total_data = [traffic_data.get(h, {'total': 0})['total'] for h in hours]

        # 绘制进出港流量对比
        width = 0.35
        x = np.arange(len(hours))

        ax1.bar(x - width/2, inbound_data, width, label='进港', color='skyblue', alpha=0.8)
        ax1.bar(x + width/2, outbound_data, width, label='出港', color='lightcoral', alpha=0.8)

        ax1.set_xlabel('时间 (小时)')
        ax1.set_ylabel('航班数量')
        ax1.set_title('24小时进出港流量预测')
        ax1.set_xticks(x)
        ax1.set_xticklabels([f'{h:02d}:00' for h in hours], rotation=45)
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 绘制总流量趋势
        ax2.plot(hours, total_data, marker='o', linewidth=2, markersize=4, color='green')
        ax2.fill_between(hours, total_data, alpha=0.3, color='green')

        ax2.set_xlabel('时间 (小时)')
        ax2.set_ylabel('总航班数量')
        ax2.set_title('24小时总流量趋势')
        ax2.set_xticks(range(0, 24, 2))
        ax2.set_xticklabels([f'{h:02d}:00' for h in range(0, 24, 2)])
        ax2.grid(True, alpha=0.3)

        # 标记繁忙时段
        ax2.axvspan(7, 23, alpha=0.2, color='yellow', label='繁忙时段')
        ax2.legend()
    
    def update_backlog_chart(self, simulation_results):
        """更新积压分析图表 - 显示推演分析指标"""
        try:
            # 清除之前的图表
            self.backlog_figure.clear()

            # 计算四个关键指标
            indicators = self._calculate_backlog_indicators(simulation_results)

            # 创建主图用于指标展示
            ax = self.backlog_figure.add_subplot(1, 1, 1)
            ax.set_xlim(0, 10)
            ax.set_ylim(0, 10)
            ax.axis('off')

            # 设置背景色
            ax.set_facecolor('#f8f9fa')

            # 添加标题
            ax.text(5, 9.5, '推演积压分析指标', fontsize=16, fontweight='bold',
                   ha='center', va='center', color='#333333')

            # 定义卡片样式函数
            def draw_indicator_card(x, y, width, height, title, icon, value, status,
                                  bg_color='#ffffff', border_color='#e0e0e0', text_color='#333333'):
                from matplotlib.patches import FancyBboxPatch

                # 绘制卡片背景
                card = FancyBboxPatch((x, y), width, height,
                                    boxstyle="round,pad=0.1",
                                    facecolor=bg_color,
                                    edgecolor=border_color,
                                    linewidth=2,
                                    alpha=0.9)
                ax.add_patch(card)

                # 标题和图标
                ax.text(x + width/2, y + height - 0.3, f'{icon} {title}',
                       fontsize=12, fontweight='bold', ha='center', va='center', color='#555555')

                # 主要数值
                ax.text(x + width/2, y + height/2, value,
                       fontsize=18, fontweight='bold', ha='center', va='center', color=text_color)

                # 状态标识
                ax.text(x + width/2, y + 0.3, f'({status})',
                       fontsize=10, ha='center', va='center', color=text_color)

            # 指标1：积压时段准确性 (左上)
            period_match = indicators['period_accuracy']
            period_bg = '#f0fff0' if period_match else '#fff0f0'
            period_border = '#51cf66' if period_match else '#ff6b6b'
            period_text = '#2b8a3e' if period_match else '#e03131'
            period_status = '达标' if period_match else '不达标'

            draw_indicator_card(1, 6.5, 3.5, 2, '积压时段准确性', '●',
                              '偏差≤1时段' if period_match else '偏差>1时段',
                              period_status, period_bg, period_border, period_text)

            # 指标2：持续时长一致性 (右上)
            duration_match = indicators['duration_consistency']
            duration_bg = '#f0fff0' if duration_match else '#fff0f0'
            duration_border = '#51cf66' if duration_match else '#ff6b6b'
            duration_text = '#2b8a3e' if duration_match else '#e03131'
            duration_status = '达标' if duration_match else '不达标'

            draw_indicator_card(5.5, 6.5, 3.5, 2, '持续时长一致性', '■',
                              '时长一致' if duration_match else '时长不一致',
                              duration_status, duration_bg, duration_border, duration_text)

            # 指标3：积压峰值准确性 (左下)
            peak_match = indicators['peak_accuracy']
            peak_bg = '#fff8f0' if not peak_match else '#f0fff0'
            peak_border = '#ff8c42' if not peak_match else '#51cf66'
            peak_text = '#d63031' if not peak_match else '#2b8a3e'
            peak_status = '达标' if peak_match else '不达标'

            peak_deviation = indicators.get('peak_deviation', 0)
            draw_indicator_card(1, 4, 3.5, 2, '积压峰值准确性', '▲',
                              f'偏差{peak_deviation:.1f}%',
                              peak_status, peak_bg, peak_border, peak_text)

            # 指标4：最晚运行时段 (右下)
            latest_match = indicators['latest_period_match']
            latest_bg = '#f0fff0' if latest_match else '#fff0f0'
            latest_border = '#51cf66' if latest_match else '#ff6b6b'
            latest_text = '#2b8a3e' if latest_match else '#e03131'
            latest_status = '达标' if latest_match else '不达标'

            draw_indicator_card(5.5, 4, 3.5, 2, '最晚运行时段', '◆',
                              '时段一致' if latest_match else '时段不一致',
                              latest_status, latest_bg, latest_border, latest_text)

            # 总体评估区域
            all_pass = all([period_match, duration_match, peak_match, latest_match])
            overall_status = '达标' if all_pass else '不达标'

            overall_colors = {
                '达标': ('#e8f5e8', '#51cf66', '#2b8a3e'),
                '不达标': ('#ffebee', '#ef5350', '#d32f2f')
            }

            bg_color, border_color, text_color = overall_colors[overall_status]

            # 总体评估卡片
            from matplotlib.patches import FancyBboxPatch
            eval_card = FancyBboxPatch((1, 1.5), 8, 1.8,
                                     boxstyle="round,pad=0.15",
                                     facecolor=bg_color,
                                     edgecolor=border_color,
                                     linewidth=3,
                                     alpha=0.9)
            ax.add_patch(eval_card)

            ax.text(5, 2.8, '总体评估', fontsize=14, fontweight='bold',
                    ha='center', va='center', color='#555555')
            ax.text(5, 2.0, overall_status, fontsize=24, fontweight='bold',
                    ha='center', va='center', color=text_color)

            # 指标说明
            ax.text(5, 0.8, '要求：四个指标全部符合才算达标',
                    fontsize=10, ha='center', style='italic', color='#868e96')

            # 调整布局
            self.backlog_figure.tight_layout()

            # 刷新画布
            self.backlog_canvas.draw()

        except Exception as e:
            # 错误处理
            self.backlog_figure.clear()
            ax = self.backlog_figure.add_subplot(111)
            ax.text(0.5, 0.5, f'积压分析错误: {str(e)}',
                   horizontalalignment='center', verticalalignment='center',
                   transform=ax.transAxes, fontsize=12, color='red')
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            ax.axis('off')
            self.backlog_canvas.draw()

    def _calculate_backlog_indicators(self, simulation_results):
        """计算积压分析的四个关键指标"""
        try:
            # 提取实际积压和预测积压的时段
            actual_backlog_periods = []
            predicted_backlog_periods = []

            for _, row in simulation_results.iterrows():
                period_hour = int(row['时段'].split(':')[0])
                actual_backlog = row['实际积压量']
                predicted_backlog = row['预测积压量']

                if actual_backlog > 0:
                    actual_backlog_periods.append(period_hour)
                if predicted_backlog > 0:
                    predicted_backlog_periods.append(period_hour)

            # 指标1：积压时段准确性（偏差不超过1个时段）
            period_accuracy = self._check_period_accuracy(actual_backlog_periods, predicted_backlog_periods)

            # 指标2：持续时长一致性
            duration_consistency = self._check_duration_consistency(actual_backlog_periods, predicted_backlog_periods)

            # 指标3：积压峰值准确性（偏差不超过15%）
            peak_accuracy, peak_deviation = self._check_peak_accuracy(simulation_results)

            # 指标4：最晚运行时段一致性
            latest_period_match = self._check_latest_period(actual_backlog_periods, predicted_backlog_periods)

            return {
                'period_accuracy': period_accuracy,
                'duration_consistency': duration_consistency,
                'peak_accuracy': peak_accuracy,
                'peak_deviation': peak_deviation,
                'latest_period_match': latest_period_match
            }

        except Exception:
            # 如果计算出错，返回默认值
            return {
                'period_accuracy': False,
                'duration_consistency': False,
                'peak_accuracy': False,
                'peak_deviation': 0.0,
                'latest_period_match': False
            }

    def _check_period_accuracy(self, actual_periods, predicted_periods):
        """检查积压时段准确性：偏差不超过1个时段"""
        if not actual_periods or not predicted_periods:
            return len(actual_periods) == len(predicted_periods)

        # 检查起始时段偏差
        actual_start = min(actual_periods)
        predicted_start = min(predicted_periods)
        start_diff = abs(actual_start - predicted_start)

        # 检查结束时段偏差
        actual_end = max(actual_periods)
        predicted_end = max(predicted_periods)
        end_diff = abs(actual_end - predicted_end)

        return start_diff <= 1 and end_diff <= 1

    def _check_duration_consistency(self, actual_periods, predicted_periods):
        """检查持续时长一致性"""
        actual_duration = len(actual_periods)
        predicted_duration = len(predicted_periods)
        return actual_duration == predicted_duration

    def _check_peak_accuracy(self, simulation_results):
        """检查积压峰值准确性：偏差不超过15%"""
        try:
            # 找到实际积压的最高峰
            actual_peak_idx = simulation_results['实际积压量'].idxmax()
            actual_peak_value = simulation_results.loc[actual_peak_idx, '实际积压量']
            actual_peak_period = simulation_results.loc[actual_peak_idx, '时段']

            # 找到预测积压的最高峰
            predicted_peak_idx = simulation_results['预测积压量'].idxmax()
            predicted_peak_value = simulation_results.loc[predicted_peak_idx, '预测积压量']
            predicted_peak_period = simulation_results.loc[predicted_peak_idx, '时段']

            # 检查峰值时段是否一致
            period_match = actual_peak_period == predicted_peak_period

            # 计算峰值偏差
            if actual_peak_value > 0:
                deviation = abs(predicted_peak_value - actual_peak_value) / actual_peak_value * 100
            else:
                deviation = 0 if predicted_peak_value == 0 else 100

            # 峰值偏差不超过15%且时段一致
            peak_accuracy = period_match and deviation <= 15

            return peak_accuracy, deviation

        except Exception:
            return False, 0.0

    def _check_latest_period(self, actual_periods, predicted_periods):
        """检查最晚运行时段一致性"""
        if not actual_periods or not predicted_periods:
            return len(actual_periods) == len(predicted_periods)

        actual_latest = max(actual_periods)
        predicted_latest = max(predicted_periods)
        return actual_latest == predicted_latest

    def update_delay_chart(self, delay_data):
        """更新延误分析图表"""
        self.delay_figure.clear()
        
        if not delay_data['affected_flights']:
            # 如果没有延误数据，显示提示信息
            ax = self.delay_figure.add_subplot(1, 1, 1)
            ax.text(0.5, 0.5, '无延误航班\n运行状态良好', 
                   horizontalalignment='center', verticalalignment='center',
                   transform=ax.transAxes, fontsize=16, color='green')
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            ax.axis('off')
        else:
            # 创建延误分析图表
            ax1 = self.delay_figure.add_subplot(2, 1, 1)
            ax2 = self.delay_figure.add_subplot(2, 1, 2)
            
            # 延误时间分布
            delay_minutes = [flight['delay_minutes'] for flight in delay_data['affected_flights']]
            
            ax1.hist(delay_minutes, bins=10, color='orange', alpha=0.7, edgecolor='black')
            ax1.set_xlabel('延误时间 (分钟)')
            ax1.set_ylabel('航班数量')
            ax1.set_title('延误时间分布')
            ax1.grid(True, alpha=0.3)
            
            # 按小时统计延误航班数
            hourly_delays = {}
            for flight in delay_data['affected_flights']:
                hour = flight['original_hour']
                hourly_delays[hour] = hourly_delays.get(hour, 0) + 1
            
            hours = sorted(hourly_delays.keys())
            counts = [hourly_delays[h] for h in hours]
            
            ax2.bar(hours, counts, color='red', alpha=0.7)
            ax2.set_xlabel('原计划时间 (小时)')
            ax2.set_ylabel('延误航班数')
            ax2.set_title('各时段延误航班统计')
            ax2.set_xticks(hours)
            ax2.set_xticklabels([f'{h:02d}:00' for h in hours])
            ax2.grid(True, alpha=0.3)
        
        self.delay_figure.tight_layout()
        self.delay_canvas.draw()
    
    def update_comparison_chart(self, results):
        """更新对比分析图表"""
        self.comparison_figure.clear()
        
        # 创建综合对比图表
        ax = self.comparison_figure.add_subplot(1, 1, 1)
        
        # 准备数据
        traffic_data = results['traffic_prediction']
        operation_data = results['operation_result']
        
        hours = list(range(7, 24))  # 繁忙时段
        planned_flights = [traffic_data.get(h, {'total': 0})['total'] for h in hours]
        processed_flights = [operation_data['hourly_status'].get(h, {'processed': 0})['processed'] for h in hours]
        
        # 绘制对比图
        x = np.arange(len(hours))
        width = 0.35
        
        ax.bar(x - width/2, planned_flights, width, label='计划航班', color='blue', alpha=0.7)
        ax.bar(x + width/2, processed_flights, width, label='实际处理', color='green', alpha=0.7)
        
        ax.set_xlabel('时间 (小时)')
        ax.set_ylabel('航班数量')
        ax.set_title('繁忙时段计划vs实际处理对比')
        ax.set_xticks(x)
        ax.set_xticklabels([f'{h:02d}:00' for h in hours], rotation=45)
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 添加统计信息
        total_planned = sum(planned_flights)
        total_processed = sum(processed_flights)
        efficiency = (total_processed / total_planned * 100) if total_planned > 0 else 0
        
        ax.text(0.02, 0.98, f'总计划航班: {total_planned}\n总处理航班: {total_processed}\n处理效率: {efficiency:.1f}%',
                transform=ax.transAxes, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        self.comparison_figure.tight_layout()
        self.comparison_canvas.draw()
    
    def clear_charts(self):
        """清空所有图表"""
        for figure in [self.traffic_figure, self.backlog_figure,
                      self.delay_figure, self.comparison_figure]:
            figure.clear()

        for canvas in [self.traffic_canvas, self.backlog_canvas,
                      self.delay_canvas, self.comparison_canvas]:
            canvas.draw()


class PredictionChartWidget(QWidget):
    """流量预测专用图表组件"""

    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)

        # 创建matplotlib图形
        self.figure = Figure(figsize=(12, 6), dpi=100)
        self.canvas = FigureCanvas(self.figure)
        layout.addWidget(self.canvas)

    def update_prediction_chart(self, prediction_df):
        """更新流量预测结果图表"""
        try:
            # 清除之前的图表
            self.figure.clear()

            # 创建主图
            ax1 = self.figure.add_subplot(111)

            # 提取时段标签（去掉时间范围，只保留小时）
            time_labels = [label.split('-')[0] for label in prediction_df['时段']]
            x_pos = range(len(time_labels))

            # 绘制流量对比柱状图（左Y轴）
            width = 0.35
            bars1 = ax1.bar([x - width/2 for x in x_pos], prediction_df['预测总流量'], width,
                           label='预测总流量', color='skyblue', alpha=0.8)
            bars2 = ax1.bar([x + width/2 for x in x_pos], prediction_df['实际总流量'], width,
                           label='实际总流量', color='orange', alpha=0.8)

            ax1.set_xlabel('时段')
            ax1.set_ylabel('航班数量', color='black')
            ax1.set_title('每小时流量预测对比与偏差率分析')
            ax1.set_xticks(x_pos)
            ax1.set_xticklabels(time_labels, rotation=45)
            ax1.grid(True, alpha=0.3)

            # 创建右Y轴用于偏差率
            ax2 = ax1.twinx()

            # 绘制偏差率折线图（右Y轴）
            deviation_rates = prediction_df['小时偏差率'] * 100  # 转换为百分比
            line = ax2.plot(x_pos, deviation_rates, color='red', marker='o', linewidth=2,
                           markersize=4, label='偏差率', alpha=0.8)

            # 添加15%阈值线
            ax2.axhline(y=15, color='red', linestyle='--', alpha=0.6, label='15%阈值线')

            ax2.set_ylabel('偏差率 (%)', color='red')
            ax2.tick_params(axis='y', labelcolor='red')

            # 合并图例
            lines1, labels1 = ax1.get_legend_handles_labels()
            lines2, labels2 = ax2.get_legend_handles_labels()
            ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left')

            # 调整布局
            self.figure.tight_layout()

            # 刷新画布
            self.canvas.draw()

        except Exception as e:
            # 错误处理
            self.figure.clear()
            ax = self.figure.add_subplot(111)
            ax.text(0.5, 0.5, f'图表生成错误: {str(e)}',
                   horizontalalignment='center', verticalalignment='center',
                   transform=ax.transAxes, fontsize=12, color='red')
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            ax.axis('off')
            self.canvas.draw()

    def clear_chart(self):
        """清空图表"""
        self.figure.clear()
        self.canvas.draw()


class BacklogAnalysisWidget(QWidget):
    """积压分析组件 - 推演结果评估"""

    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)

        # 创建matplotlib图形
        self.figure = Figure(figsize=(12, 8), dpi=100)
        self.canvas = FigureCanvas(self.figure)
        layout.addWidget(self.canvas)

    def update_backlog_analysis(self, simulation_results):
        """更新积压分析结果"""
        try:
            # 清除之前的图表
            self.figure.clear()

            # 计算四个关键指标
            indicators = self._calculate_indicators(simulation_results)

            # 创建主图用于指标展示
            ax = self.figure.add_subplot(1, 1, 1)
            ax.set_xlim(0, 10)
            ax.set_ylim(0, 10)
            ax.axis('off')

            # 设置背景色
            ax.set_facecolor('#f8f9fa')

            # 添加标题
            ax.text(5, 9.5, '推演积压分析指标', fontsize=16, fontweight='bold',
                   ha='center', va='center', color='#333333')

            # 定义卡片样式函数
            def draw_indicator_card(x, y, width, height, title, icon, value, status,
                                  bg_color='#ffffff', border_color='#e0e0e0', text_color='#333333'):
                from matplotlib.patches import FancyBboxPatch

                # 绘制卡片背景
                card = FancyBboxPatch((x, y), width, height,
                                    boxstyle="round,pad=0.1",
                                    facecolor=bg_color,
                                    edgecolor=border_color,
                                    linewidth=2,
                                    alpha=0.9)
                ax.add_patch(card)

                # 标题和图标
                ax.text(x + width/2, y + height - 0.3, f'{icon} {title}',
                       fontsize=12, fontweight='bold', ha='center', va='center', color='#555555')

                # 主要数值
                ax.text(x + width/2, y + height/2, value,
                       fontsize=18, fontweight='bold', ha='center', va='center', color=text_color)

                # 状态标识
                ax.text(x + width/2, y + 0.3, f'({status})',
                       fontsize=10, ha='center', va='center', color=text_color)

            # 指标1：积压时段准确性 (左上)
            period_match = indicators['period_accuracy']
            period_bg = '#f0fff0' if period_match else '#fff0f0'
            period_border = '#51cf66' if period_match else '#ff6b6b'
            period_text = '#2b8a3e' if period_match else '#e03131'
            period_status = '达标' if period_match else '不达标'

            draw_indicator_card(1, 6.5, 3.5, 2, '积压时段准确性', '⏰',
                              '偏差≤1时段' if period_match else '偏差>1时段',
                              period_status, period_bg, period_border, period_text)

            # 指标2：持续时长一致性 (右上)
            duration_match = indicators['duration_consistency']
            duration_bg = '#f0fff0' if duration_match else '#fff0f0'
            duration_border = '#51cf66' if duration_match else '#ff6b6b'
            duration_text = '#2b8a3e' if duration_match else '#e03131'
            duration_status = '达标' if duration_match else '不达标'

            draw_indicator_card(5.5, 6.5, 3.5, 2, '持续时长一致性', '⏱',
                              '时长一致' if duration_match else '时长不一致',
                              duration_status, duration_bg, duration_border, duration_text)

            # 指标3：积压峰值准确性 (左下)
            peak_match = indicators['peak_accuracy']
            peak_bg = '#fff8f0' if not peak_match else '#f0fff0'
            peak_border = '#ff8c42' if not peak_match else '#51cf66'
            peak_text = '#d63031' if not peak_match else '#2b8a3e'
            peak_status = '达标' if peak_match else '不达标'

            peak_deviation = indicators.get('peak_deviation', 0)
            draw_indicator_card(1, 4, 3.5, 2, '积压峰值准确性', '📊',
                              f'偏差{peak_deviation:.1f}%',
                              peak_status, peak_bg, peak_border, peak_text)

            # 指标4：最晚运行时段 (右下)
            latest_match = indicators['latest_period_match']
            latest_bg = '#f0fff0' if latest_match else '#fff0f0'
            latest_border = '#51cf66' if latest_match else '#ff6b6b'
            latest_text = '#2b8a3e' if latest_match else '#e03131'
            latest_status = '达标' if latest_match else '不达标'

            draw_indicator_card(5.5, 4, 3.5, 2, '最晚运行时段', '🕐',
                              '时段一致' if latest_match else '时段不一致',
                              latest_status, latest_bg, latest_border, latest_text)

            # 总体评估区域
            all_pass = all([period_match, duration_match, peak_match, latest_match])
            overall_status = '达标' if all_pass else '不达标'

            overall_colors = {
                '达标': ('#e8f5e8', '#51cf66', '#2b8a3e'),
                '不达标': ('#ffebee', '#ef5350', '#d32f2f')
            }

            bg_color, border_color, text_color = overall_colors[overall_status]

            # 总体评估卡片
            from matplotlib.patches import FancyBboxPatch
            eval_card = FancyBboxPatch((1, 1.5), 8, 1.8,
                                     boxstyle="round,pad=0.15",
                                     facecolor=bg_color,
                                     edgecolor=border_color,
                                     linewidth=3,
                                     alpha=0.9)
            ax.add_patch(eval_card)

            ax.text(5, 2.8, '总体评估', fontsize=14, fontweight='bold',
                    ha='center', va='center', color='#555555')
            ax.text(5, 2.0, overall_status, fontsize=24, fontweight='bold',
                    ha='center', va='center', color=text_color)

            # 指标说明
            ax.text(5, 0.8, '要求：四个指标全部符合才算达标',
                    fontsize=10, ha='center', style='italic', color='#868e96')

            # 调整布局
            self.figure.tight_layout()

            # 刷新画布
            self.canvas.draw()

        except Exception as e:
            # 错误处理
            self.figure.clear()
            ax = self.figure.add_subplot(111)
            ax.text(0.5, 0.5, f'积压分析错误: {str(e)}',
                   horizontalalignment='center', verticalalignment='center',
                   transform=ax.transAxes, fontsize=12, color='red')
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            ax.axis('off')
            self.canvas.draw()

    def _calculate_indicators(self, simulation_results):
        """计算四个关键指标"""
        try:
            # 提取实际积压和预测积压的时段
            actual_backlog_periods = []
            predicted_backlog_periods = []

            for i, row in simulation_results.iterrows():
                period_hour = int(row['时段'].split(':')[0])
                actual_backlog = row['实际积压量']
                predicted_backlog = row['预测积压量']

                if actual_backlog > 0:
                    actual_backlog_periods.append(period_hour)
                if predicted_backlog > 0:
                    predicted_backlog_periods.append(period_hour)

            # 指标1：积压时段准确性（偏差不超过1个时段）
            period_accuracy = self._check_period_accuracy(actual_backlog_periods, predicted_backlog_periods)

            # 指标2：持续时长一致性
            duration_consistency = self._check_duration_consistency(actual_backlog_periods, predicted_backlog_periods)

            # 指标3：积压峰值准确性（偏差不超过15%）
            peak_accuracy, peak_deviation = self._check_peak_accuracy(simulation_results)

            # 指标4：最晚运行时段一致性
            latest_period_match = self._check_latest_period(actual_backlog_periods, predicted_backlog_periods)

            return {
                'period_accuracy': period_accuracy,
                'duration_consistency': duration_consistency,
                'peak_accuracy': peak_accuracy,
                'peak_deviation': peak_deviation,
                'latest_period_match': latest_period_match
            }

        except Exception as e:
            # 如果计算出错，返回默认值
            return {
                'period_accuracy': False,
                'duration_consistency': False,
                'peak_accuracy': False,
                'peak_deviation': 0.0,
                'latest_period_match': False
            }

    def _check_period_accuracy(self, actual_periods, predicted_periods):
        """检查积压时段准确性：偏差不超过1个时段"""
        if not actual_periods or not predicted_periods:
            return len(actual_periods) == len(predicted_periods)

        # 检查起始时段偏差
        actual_start = min(actual_periods)
        predicted_start = min(predicted_periods)
        start_diff = abs(actual_start - predicted_start)

        # 检查结束时段偏差
        actual_end = max(actual_periods)
        predicted_end = max(predicted_periods)
        end_diff = abs(actual_end - predicted_end)

        return start_diff <= 1 and end_diff <= 1

    def _check_duration_consistency(self, actual_periods, predicted_periods):
        """检查持续时长一致性"""
        actual_duration = len(actual_periods)
        predicted_duration = len(predicted_periods)
        return actual_duration == predicted_duration

    def _check_peak_accuracy(self, simulation_results):
        """检查积压峰值准确性：偏差不超过15%"""
        try:
            # 找到实际积压的最高峰
            actual_peak_idx = simulation_results['实际积压量'].idxmax()
            actual_peak_value = simulation_results.loc[actual_peak_idx, '实际积压量']
            actual_peak_period = simulation_results.loc[actual_peak_idx, '时段']

            # 找到预测积压的最高峰
            predicted_peak_idx = simulation_results['预测积压量'].idxmax()
            predicted_peak_value = simulation_results.loc[predicted_peak_idx, '预测积压量']
            predicted_peak_period = simulation_results.loc[predicted_peak_idx, '时段']

            # 检查峰值时段是否一致
            period_match = actual_peak_period == predicted_peak_period

            # 计算峰值偏差
            if actual_peak_value > 0:
                deviation = abs(predicted_peak_value - actual_peak_value) / actual_peak_value * 100
            else:
                deviation = 0 if predicted_peak_value == 0 else 100

            # 峰值偏差不超过15%且时段一致
            peak_accuracy = period_match and deviation <= 15

            return peak_accuracy, deviation

        except Exception:
            return False, 0.0

    def _check_latest_period(self, actual_periods, predicted_periods):
        """检查最晚运行时段一致性"""
        if not actual_periods or not predicted_periods:
            return len(actual_periods) == len(predicted_periods)

        actual_latest = max(actual_periods)
        predicted_latest = max(predicted_periods)
        return actual_latest == predicted_latest

    def clear_chart(self):
        """清空图表"""
        self.figure.clear()
        self.canvas.draw()


class DelayChartWidget(QWidget):
    """延误预测图表组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)

        # 创建matplotlib图形
        self.figure = Figure(figsize=(12, 8))
        self.canvas = FigureCanvas(self.figure)
        layout.addWidget(self.canvas)

        # 初始化显示
        self.show_empty_chart()

    def show_empty_chart(self):
        """显示空图表"""
        self.figure.clear()
        ax = self.figure.add_subplot(1, 1, 1)
        ax.text(0.5, 0.5, '请先进行延误预测',
                ha='center', va='center', fontsize=16,
                transform=ax.transAxes, color='gray')
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')
        self.canvas.draw()

    def update_delay_chart(self, delay_data):
        """更新延误预测图表 - 预测延误 vs 实际延误对比"""
        if delay_data is None or delay_data.empty:
            self.show_empty_chart()
            return

        self.figure.clear()
        ax = self.figure.add_subplot(1, 1, 1)

        try:
            # 检查必要的列是否存在
            required_columns = ['计划离港时间', '实际离港时间', '预测离港延误(分钟)']
            if not all(col in delay_data.columns for col in required_columns):
                ax.text(0.5, 0.5, '延误预测数据不完整\n缺少必要字段：计划离港时间、实际离港时间、预测离港延误',
                       ha='center', va='center', fontsize=14,
                       transform=ax.transAxes, color='orange')
                ax.axis('off')
                self.figure.tight_layout()
                self.canvas.draw()
                return

            # 计算实际延误时间（实际离港时间 - 计划离港时间 - 15分钟）
            actual_delays = []
            predicted_delays = []
            time_labels = []

            for _, row in delay_data.iterrows():
                try:
                    # 解析时间
                    planned_time = pd.to_datetime(f"2025-01-27 {row['计划离港时间']}")
                    actual_time = pd.to_datetime(f"2025-01-27 {row['实际离港时间']}")

                    # 计算实际延误（分钟）- 减去15分钟缓冲时间
                    actual_delay = (actual_time - planned_time).total_seconds() / 60 - 15
                    predicted_delay = float(row['预测离港延误(分钟)'])

                    actual_delays.append(actual_delay)
                    predicted_delays.append(predicted_delay)
                    time_labels.append(row['计划离港时间'])

                except Exception:
                    continue

            if not actual_delays:
                ax.text(0.5, 0.5, '无法解析延误数据\n请检查时间格式',
                       ha='center', va='center', fontsize=14,
                       transform=ax.transAxes, color='red')
                ax.axis('off')
                self.figure.tight_layout()
                self.canvas.draw()
                return

            # 按计划离港时间排序
            sorted_data = sorted(zip(time_labels, actual_delays, predicted_delays))
            time_labels, actual_delays, predicted_delays = zip(*sorted_data)

            # 创建X轴位置
            x_pos = range(len(time_labels))

            # 绘制对比图
            width = 0.35
            ax.bar([x - width/2 for x in x_pos], actual_delays, width,
                   label='实际延误', color='red', alpha=0.7)
            ax.bar([x + width/2 for x in x_pos], predicted_delays, width,
                   label='预测延误', color='blue', alpha=0.7)

            # 添加零延误基准线
            ax.axhline(y=0, color='green', linestyle='-', alpha=0.5, label='准点基准')

            # 设置坐标轴
            ax.set_xlabel('计划离港时间', fontsize=12)
            ax.set_ylabel('延误时间 (分钟)', fontsize=12)
            ax.set_title('延误预测准确性分析', fontsize=16, fontweight='bold')

            # 设置X轴标签（每隔几个显示，避免重叠）
            step = max(1, len(time_labels) // 10)  # 最多显示10个标签
            ax.set_xticks([i for i in range(0, len(time_labels), step)])
            ax.set_xticklabels([time_labels[i] for i in range(0, len(time_labels), step)],
                              rotation=45)

            # 添加图例
            ax.legend()
            ax.grid(True, alpha=0.3)

            # 计算预测准确性统计
            mae = sum(abs(a - p) for a, p in zip(actual_delays, predicted_delays)) / len(actual_delays)
            rmse = (sum((a - p)**2 for a, p in zip(actual_delays, predicted_delays)) / len(actual_delays))**0.5

            # 添加统计信息
            stats_text = f"""预测准确性统计：
• 平均绝对误差: {mae:.1f} 分钟
• 均方根误差: {rmse:.1f} 分钟
• 样本数量: {len(actual_delays)} 个航班"""

            ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=10,
                   verticalalignment='top', bbox=dict(boxstyle="round,pad=0.3",
                   facecolor="lightblue", alpha=0.8))

        except Exception as e:
            # 如果出现错误，显示错误信息
            ax.text(0.5, 0.5, f'图表生成失败\n{str(e)}',
                    ha='center', va='center', fontsize=14,
                    transform=ax.transAxes, color='red')
            ax.axis('off')

        self.figure.tight_layout()
        self.canvas.draw()
