#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据生成器模块
用于生成广州白云机场的模拟航班数据
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import random
import os

class DataGenerator:
    """数据生成器类"""

    def __init__(self):
        self.airlines = ['CZ', 'CA', 'MU', '3U', 'HU', 'FM', 'GJ', 'JD', 'EU', 'KN']
        self.aircraft_types = ['A320', 'A321', 'A330', 'A350', 'B737', 'B738', 'B777', 'B787']
        # 机场代码映射
        self.airports = {
            'ZBAA': '北京首都', 'ZSPD': '上海浦东', 'ZGSZ': '深圳宝安', 'ZUUU': '成都双流',
            'ZUCK': '重庆江北', 'ZLXY': '西安咸阳', 'ZSHC': '杭州萧山', 'ZSNJ': '南京禄口',
            'ZHWH': '武汉天河', 'ZGHA': '长沙黄花', 'ZPPP': '昆明长水', 'ZSAM': '厦门高崎',
            'ZSQD': '青岛流亭', 'ZYTL': '大连周水子', 'ZYTX': '沈阳桃仙', 'ZYHB': '哈尔滨太平',
            'ZWWW': '乌鲁木齐地窝堡', 'ZLLL': '兰州中川', 'ZLIC': '银川河东', 'ZBHH': '呼和浩特白塔',
            'ZBYN': '太原武宿', 'ZBSJ': '石家庄正定', 'ZSJN': '济南遥墙', 'ZHCC': '郑州新郑',
            'ZSOF': '合肥新桥', 'ZSCN': '南昌昌北', 'ZSFZ': '福州长乐', 'ZJHK': '海口美兰',
            'ZJSY': '三亚凤凰', 'ZGKL': '桂林两江', 'ZGNN': '南宁吴圩'
        }
    
    def generate_flight_schedule(self, target_date='2025-01-01'):
        """
        生成某一天7:00-23:00的航班计划数据

        Args:
            target_date: 目标日期

        Returns:
            DataFrame: 航班计划数据
        """
        flights = []
        flight_id = 1

        target_dt = datetime.strptime(target_date, '%Y-%m-%d')

        # 生成7:00-23:00的航班，每天约400-500个航班
        daily_flights = random.randint(400, 500)

        # 获取其他机场代码列表
        other_airports = list(self.airports.keys())

        for _ in range(daily_flights):
            # 随机选择航空公司和机型
            airline = random.choice(self.airlines)
            aircraft_type = random.choice(self.aircraft_types)

            # 生成航班号
            flight_no = f"{airline}{random.randint(1000, 9999)}"

            # 随机决定是进港还是出港
            is_departure = random.choice([True, False])

            # 选择另一个机场
            other_airport = random.choice(other_airports)

            if is_departure:  # 出港航班
                planned_dep_airport = 'ZGGG'  # 广州白云
                planned_arr_airport = other_airport
                actual_dep_airport = 'ZGGG'
                actual_arr_airport = other_airport
            else:  # 进港航班
                planned_dep_airport = other_airport
                planned_arr_airport = 'ZGGG'  # 广州白云
                actual_dep_airport = other_airport
                actual_arr_airport = 'ZGGG'

            # 生成7-23点的时间
            hour = random.randint(7, 23)
            minute = random.choice([0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55])

            # 计划离港时间
            planned_dep_time = target_dt.replace(hour=hour, minute=minute)

            # 飞行时间（根据距离估算，1-4小时）
            flight_duration = random.randint(60, 240)  # 分钟
            planned_arr_time = planned_dep_time + timedelta(minutes=flight_duration)

            # 生成实际时间（可能有延误）
            dep_delay = 0
            arr_delay = 0

            delay_prob = random.random()
            if delay_prob < 0.75:  # 75%准点
                dep_delay = random.randint(0, 15)
                arr_delay = dep_delay + random.randint(-5, 10)
            elif delay_prob < 0.9:  # 15%轻微延误
                dep_delay = random.randint(16, 60)
                arr_delay = dep_delay + random.randint(-10, 20)
            else:  # 10%严重延误
                dep_delay = random.randint(61, 180)
                arr_delay = dep_delay + random.randint(-15, 30)

            # 确保延误不为负数
            arr_delay = max(0, arr_delay)

            actual_dep_time = planned_dep_time + timedelta(minutes=dep_delay)
            actual_arr_time = planned_arr_time + timedelta(minutes=arr_delay)

            flight = {
                'flight_id': flight_id,
                'date': target_dt.strftime('%Y-%m-%d'),
                'flight_no': flight_no,
                'aircraft_type': aircraft_type,
                'planned_dep_time': planned_dep_time.strftime('%H:%M'),
                'actual_dep_time': actual_dep_time.strftime('%H:%M'),
                'planned_arr_time': planned_arr_time.strftime('%H:%M'),
                'actual_arr_time': actual_arr_time.strftime('%H:%M'),
                'planned_dep_airport': planned_dep_airport,
                'planned_arr_airport': planned_arr_airport,
                'actual_dep_airport': actual_dep_airport,
                'actual_arr_airport': actual_arr_airport,
                'dep_delay_minutes': dep_delay,
                'arr_delay_minutes': arr_delay,
                'status': '正常' if max(dep_delay, arr_delay) <= 15 else '延误'
            }

            flights.append(flight)
            flight_id += 1

        # 按计划离港时间排序
        df = pd.DataFrame(flights)
        df = df.sort_values('planned_dep_time').reset_index(drop=True)

        return df
    

    
    def save_data_files(self, output_dir='data', target_date='2025-01-01'):
        """
        保存数据文件

        Args:
            output_dir: 输出目录
            target_date: 目标日期
        """
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        # 生成航班数据
        print(f"正在生成{target_date}的航班计划数据...")
        flight_data = self.generate_flight_schedule(target_date)
        flight_file = os.path.join(output_dir, f'广州白云机场航班计划_{target_date}.xlsx')
        flight_data.to_excel(flight_file, index=False, sheet_name='航班计划')
        print(f"航班数据已保存到: {flight_file}")
        print(f"共生成 {len(flight_data)} 条航班记录")

        return flight_file

if __name__ == "__main__":
    generator = DataGenerator()
    # 生成今天的航班数据
    generator.save_data_files(target_date='2025-01-27')
